"""
User command handlers for the Telegram bot
"""

import asyncio
import pytz
from datetime import datetime, timezone
from telegram import Update, InlineKeyboardMarkup, InlineKeyboardButton
from telegram.ext import ContextTypes

from config import Config
from src.models.transaction import TransactionType
from src.utils.logger import setup_logger, log_user_action

logger = setup_logger(__name__)


class UserHandlers:
    """Handles user-related commands and interactions"""
    
    def __init__(self, bot_instance):
        self.bot = bot_instance
        self.services = bot_instance.services
    
    async def handle_daily_bonus(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle daily bonus claiming with enhanced 2-3 second animation"""
        try:
            user_id = update.effective_user.id
            
            # Check permissions
            permissions = await self.bot.check_user_permissions(user_id)
            if not permissions['allowed']:
                await update.message.reply_text(permissions['reason'])
                return
            
            # Get user
            user = await self.services['user'].get_user(user_id)
            if not user:
                await update.message.reply_text("❌ User not found. Please use /start first.")
                return
            
            # Check if daily bonus can be claimed
            can_claim, time_left = await self.services['user'].can_claim_daily_bonus(user_id)
            
            if not can_claim:
                hours = int(time_left // 3600)
                minutes = int((time_left % 3600) // 60)
                
                cooldown_text = f"""
🕐 **Daily Bonus Cooldown**

⏰ **Next claim in:** {hours}h {minutes}m
💎 **Daily gift:** 💎{Config.DAILY_BONUS_AMOUNT}

*Come back later for your daily reward!*
                """
                
                keyboard = [[InlineKeyboardButton("🔄 Refresh", callback_data="balance_refresh")]]
                reply_markup = InlineKeyboardMarkup(keyboard)
                
                await update.message.reply_text(
                    cooldown_text,
                    parse_mode='Markdown',
                    reply_markup=reply_markup
                )
                return
            
            # Start animated claiming sequence
            message = await update.message.reply_text("🎁 **Claiming daily bonus...**")
            
            # Animation frames
            frames = [
                "🎁 **Claiming daily bonus...**",
                "🎁 **Claiming daily bonus...** ⏳",
                "🎁 **Processing reward...** 💫",
                "🎁 **Almost ready...** ✨",
                "🎁 **Success!** 🎉"
            ]
            
            # Animate the claiming process
            for frame in frames:
                await message.edit_text(frame, parse_mode='Markdown')
                await asyncio.sleep(0.4)  # 400ms delay between frames
            
            # Claim the bonus
            daily_bonus_amount = await self.bot.get_setting('daily_bonus_amount', Config.DAILY_BONUS_AMOUNT)
            success = await self.services['user'].claim_daily_bonus(user_id, daily_bonus_amount)
            
            if success:
                # Update user balance
                user = await self.services['user'].get_user(user_id)
                
                # Log the action
                await log_user_action(user_id, 'DAILY_BONUS_CLAIMED', f"Claimed daily bonus: 💎{daily_bonus_amount}")
                
                # Create transaction record
                try:
                    await self.services['transaction'].create_transaction(
                        user_id=user_id,
                        amount=daily_bonus_amount,
                        transaction_type=TransactionType.DAILY_BONUS,
                        description="Daily bonus claimed"
                    )
                except Exception as e:
                    logger.error(f"Failed to create transaction: {e}")
                
                # Concise success message
                bonus_text = f"""
🎉 **Daily Bonus Claimed!**

💰 **+💎{daily_bonus_amount}** added to your balance
💎 **New Balance:** **💎{user.balance:.2f}**

*Come back tomorrow for another gift!* 🎁
                """
                
                keyboard = [[InlineKeyboardButton("💰 View Balance", callback_data="balance_refresh")]]
                reply_markup = InlineKeyboardMarkup(keyboard)
                
                await message.edit_text(
                    bonus_text,
                    parse_mode='Markdown',
                    reply_markup=reply_markup
                )
            else:
                await message.edit_text("❌ Failed to claim daily bonus. Please try again.")
                
        except Exception as e:
            logger.error(f"Error in daily bonus: {e}")
            await update.message.reply_text("❌ Failed to claim daily bonus.")
    
    async def handle_referrals_menu(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle referrals menu with immediate display"""
        try:
            user_id = update.effective_user.id
            
            # Check permissions
            permissions = await self.bot.check_user_permissions(user_id)
            if not permissions['allowed']:
                await update.message.reply_text(permissions['reason'])
                return
            
            # Get user and referral stats
            user = await self.services['user'].get_user(user_id)
            if not user:
                await update.message.reply_text("❌ User not found. Please use /start first.")
                return
            
            # Get referral statistics
            try:
                referral_stats = await self.services['referral'].get_user_referral_stats(user_id)
                referral_count = referral_stats.get('successful_referrals', 0)
                total_referrals = referral_stats.get('total_referrals', 0)
            except:
                referral_count = user.successful_referrals
                total_referrals = 0
            
            # Calculate earnings and success rate
            referral_earnings = referral_count * Config.REFERRAL_REWARD
            success_rate = (referral_count / total_referrals * 100) if total_referrals > 0 else 0
            
            referral_text = f"""
👥 **REFERRAL SYSTEM**

• **Successful Referrals:** **{referral_count}** (*💎{referral_earnings:.2f} earned*)
• **Reward per Friend:** **💎{Config.REFERRAL_REWARD}**
• **Success Rate:** *{success_rate:.1f}%*

*Share your link and earn 💎{Config.REFERRAL_REWARD} per friend!*
            """
            
            # Create share URL
            share_url = f"https://t.me/share/url?text=https://t.me/{Config.BOT_USERNAME}?start={user.referral_code}%0AI've%20Got%20Up%20To%20💎100!%20Click%20URL%20To%20Join%20&%20Make%20Money%20Now!"
            
            keyboard = [
                [InlineKeyboardButton("📋 Copy Referral Link", callback_data="copy_referral_link")],
                [InlineKeyboardButton("📤 Share on Telegram", url=share_url)],
                [InlineKeyboardButton("🔄 Refresh Stats", callback_data="balance_refresh")]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)
            
            await update.message.reply_text(
                referral_text,
                parse_mode='Markdown',
                reply_markup=reply_markup
            )
            
        except Exception as e:
            logger.error(f"Error in referrals menu: {e}")
            await update.message.reply_text("❌ Failed to get referral information.")
    
    async def handle_tasks_menu(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle tasks menu with admin-configurable tasks system"""
        try:
            user_id = update.effective_user.id

            # Check permissions
            permissions = await self.bot.check_user_permissions(user_id)
            if not permissions['allowed']:
                await update.message.reply_text(permissions['reason'])
                return

            # Get available tasks from task management service
            task_service = self.services.get('task_management')
            if not task_service:
                await update.message.reply_text("❌ Task system not available.")
                return

            try:
                available_tasks = await task_service.get_available_tasks_for_user(user_id)
            except Exception as e:
                logger.error(f"Error getting tasks: {e}")
                available_tasks = []

            if not available_tasks:
                no_tasks_text = """
📋 **TASKS CENTER**

🚧 **No tasks available right now**

*Check back later for new earning opportunities!*
                """

                keyboard = [[InlineKeyboardButton("🔄 Refresh", callback_data="tasks_refresh")]]
                reply_markup = InlineKeyboardMarkup(keyboard)

                await update.message.reply_text(
                    no_tasks_text,
                    parse_mode='Markdown',
                    reply_markup=reply_markup
                )
                return

            # Calculate total available rewards
            total_available = sum(task['reward'] for task in available_tasks if task['status'] == 'available')

            tasks_text = f"""
📋 **TASKS CENTER**

**Available Rewards:** 💎{total_available:.2f}

"""

            # Add each task to the display with detailed formatting
            for task in available_tasks:
                if task['status'] == 'completed':
                    status_icon = "✅"
                    status_text = "COMPLETED"
                elif task['status'] == 'pending_review':
                    status_icon = "⏳"
                    status_text = "PENDING REVIEW"
                elif task['status'] == 'in_progress':
                    status_icon = "🔄"
                    status_text = "IN PROGRESS"
                else:
                    status_icon = "💎"
                    status_text = "AVAILABLE"

                tasks_text += f"{status_icon} **{task['title']}** - 💎{task['reward']} ({status_text})\n"

            tasks_text += "\n*Complete tasks to earn rewards!*"

            # Create task buttons for available tasks
            keyboard = []
            for task in available_tasks:
                if task['status'] == 'available':
                    button_text = f"🎯 {task['title']} (+💎{task['reward']})"
                    keyboard.append([InlineKeyboardButton(button_text, callback_data=f"task_{task['task_id']}")])
                elif task['status'] == 'pending_review':
                    button_text = f"⏳ {task['title']} (Under Review)"
                    keyboard.append([InlineKeyboardButton(button_text, callback_data=f"task_status_{task['task_id']}")])

            # Add refresh button
            keyboard.append([InlineKeyboardButton("🔄 Refresh Tasks", callback_data="tasks_refresh")])

            reply_markup = InlineKeyboardMarkup(keyboard)

            await update.message.reply_text(
                tasks_text,
                parse_mode='Markdown',
                reply_markup=reply_markup
            )

        except Exception as e:
            logger.error(f"Error in tasks menu: {e}")
            await update.message.reply_text("❌ Failed to load tasks.")

    async def handle_task_interaction(self, query, context, task_id):
        """Handle user interaction with a specific task"""
        try:
            user_id = query.from_user.id

            # Get task details
            task_service = self.services.get('task_management')
            if not task_service:
                await query.edit_message_text("❌ Task system not available.")
                return

            task = await task_service.get_task(task_id)
            if not task:
                await query.edit_message_text("❌ Task not found.")
                return

            # Handle different task types
            if task.is_join_channel_task():
                await self.handle_join_channel_task(query, context, task)
            elif task.is_image_submission_task():
                await self.handle_image_submission_task(query, context, task)
            else:
                await query.edit_message_text("❌ Unknown task type.")

        except Exception as e:
            logger.error(f"Error handling task interaction: {e}")
            await query.answer("❌ Failed to process task.")

    async def handle_join_channel_task(self, query, context, task):
        """Handle join channel task interaction"""
        try:
            task_text = f"""
📺 **{task.task_name}**

**Reward:** 💎{task.reward_amount}

**Instructions:**
1. Click "Join Channel" below
2. Join the channel
3. Click "Verify Membership" to claim your reward

**Channel:** {task.channel_id}
            """

            keyboard = [
                [InlineKeyboardButton("📺 Join Channel", url=task.join_link)],
                [InlineKeyboardButton("✅ Verify Membership", callback_data=f"verify_channel_{task.task_id}")],
                [InlineKeyboardButton("🔙 Back to Tasks", callback_data="tasks_refresh")]
            ]

            reply_markup = InlineKeyboardMarkup(keyboard)

            await query.edit_message_text(
                task_text,
                parse_mode='Markdown',
                reply_markup=reply_markup
            )

        except Exception as e:
            logger.error(f"Error handling join channel task: {e}")
            await query.answer("❌ Failed to display task.")

    async def handle_image_submission_task(self, query, context, task):
        """Handle image submission task interaction"""
        try:
            task_text = f"""
📸 **{task.task_name}**

**Reward:** 💎{task.reward_amount}

**Instructions:**
{task.caption}

**Steps:**
1. Complete the required action
2. Take a screenshot/photo as proof
3. Click "Submit Proof" below
4. Upload your image

⚠️ **Warning:** Submitting fake or incorrect screenshots may result in permanent ban.
            """

            keyboard = [
                [InlineKeyboardButton("📸 Submit Proof", callback_data=f"submit_image_{task.task_id}")],
                [InlineKeyboardButton("🔙 Back to Tasks", callback_data="tasks_refresh")]
            ]

            reply_markup = InlineKeyboardMarkup(keyboard)

            await query.edit_message_text(
                task_text,
                parse_mode='Markdown',
                reply_markup=reply_markup
            )

        except Exception as e:
            logger.error(f"Error handling image submission task: {e}")
            await query.answer("❌ Failed to display task.")

    async def verify_channel_membership(self, query, context, task_id):
        """Verify user's channel membership for join channel task"""
        try:
            user_id = query.from_user.id

            task_service = self.services.get('task_management')
            if not task_service:
                await query.edit_message_text("❌ Task system not available.")
                return

            # Process the join channel task
            success, message, reward = await task_service.process_join_channel_task(user_id, task_id)

            if success:
                success_text = f"""
✅ **Channel Membership Verified!**

🎉 Congratulations! You have successfully completed the task.

💰 **Reward:** 💎{reward} has been added to your balance!

*Thank you for joining our channel!*
                """

                keyboard = [
                    [InlineKeyboardButton("💰 View Balance", callback_data="balance_refresh")],
                    [InlineKeyboardButton("📋 More Tasks", callback_data="tasks_refresh")]
                ]

            else:
                success_text = f"""
❌ **Verification Failed**

{message}

Please make sure you have joined the channel and try again.
                """

                keyboard = [
                    [InlineKeyboardButton("🔄 Try Again", callback_data=f"task_{task_id}")],
                    [InlineKeyboardButton("🔙 Back to Tasks", callback_data="tasks_refresh")]
                ]

            reply_markup = InlineKeyboardMarkup(keyboard)

            await query.edit_message_text(
                success_text,
                parse_mode='Markdown',
                reply_markup=reply_markup
            )

        except Exception as e:
            logger.error(f"Error verifying channel membership: {e}")
            await query.answer("❌ Failed to verify membership.")

    async def initiate_image_submission(self, query, context, task_id):
        """Initiate image submission process"""
        try:
            # Store submission state
            context.user_data['image_submission'] = {
                'task_id': task_id,
                'step': 'waiting_for_image'
            }

            submission_text = """
📸 **Submit Your Proof**

Please upload your screenshot or image as proof that you completed the task.

**Important:**
• Upload a clear, readable image
• Make sure it shows the required proof
• Only submit genuine screenshots

Send your image now:
            """

            await query.edit_message_text(
                submission_text,
                parse_mode='Markdown'
            )

        except Exception as e:
            logger.error(f"Error initiating image submission: {e}")
            await query.answer("❌ Failed to start submission process.")

    async def process_image_submission(self, update, context):
        """Process uploaded image for task submission"""
        try:
            if 'image_submission' not in context.user_data:
                await update.message.reply_text("❌ No active image submission session.")
                return

            submission_data = context.user_data['image_submission']
            task_id = submission_data['task_id']
            user_id = update.effective_user.id

            # Check if message contains an image
            if not update.message.photo:
                await update.message.reply_text(
                    "❌ Please send an image file.\n\n"
                    "You can also send text along with the image if needed."
                )
                return

            # Get the largest photo
            photo = update.message.photo[-1]

            # In a real implementation, you would:
            # 1. Download the image
            # 2. Store it securely (cloud storage, local filesystem, etc.)
            # 3. Get the file path/URL for database storage

            # For this implementation, we'll use the file_id as a placeholder
            image_path = photo.file_id
            submission_text = update.message.caption or ""

            # Submit the task
            task_service = self.services.get('task_management')
            if not task_service:
                await update.message.reply_text("❌ Task system not available.")
                return

            success, message = await task_service.submit_task(
                user_id, task_id, image_path, submission_text
            )

            if success:
                success_text = """
✅ **Submission Received!**

Your image has been submitted for admin review.

⏳ **Status:** Pending Review
🕐 **Review Time:** Usually within 24 hours

You will be notified once your submission is reviewed.
                """

                keyboard = [
                    [InlineKeyboardButton("📋 Back to Tasks", callback_data="tasks_refresh")]
                ]

            else:
                success_text = f"❌ **Submission Failed**\n\n{message}"

                keyboard = [
                    [InlineKeyboardButton("🔄 Try Again", callback_data=f"submit_image_{task_id}")],
                    [InlineKeyboardButton("🔙 Back to Tasks", callback_data="tasks_refresh")]
                ]

            reply_markup = InlineKeyboardMarkup(keyboard)

            await update.message.reply_text(
                success_text,
                parse_mode='Markdown',
                reply_markup=reply_markup
            )

            # Clear submission state
            del context.user_data['image_submission']

        except Exception as e:
            logger.error(f"Error processing image submission: {e}")
            await update.message.reply_text("❌ Failed to process image submission.")
