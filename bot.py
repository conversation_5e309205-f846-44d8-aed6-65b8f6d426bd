#!/usr/bin/env python3
"""
Streamlined Telegram Referral Earning Bot - Production Version
"""

import asyncio
import pytz
from datetime import datetime, timezone
from telegram import Update, ReplyKeyboardMarkup, KeyboardButton
from telegram.ext import Application, <PERSON><PERSON><PERSON><PERSON>, Message<PERSON><PERSON><PERSON>, CallbackQueryHandler, filters, ContextTypes

from config import Config
from src.database import Database
from src.models.transaction import TransactionType
from src.services.user_service import UserService
from src.services.referral_service import ReferralService
from src.services.transaction_service import TransactionService
from src.services.withdrawal_service import WithdrawalService
from src.services.channel_service import ChannelService
from src.services.product_service import ProductService
from src.services.admin_settings_service import AdminSettingsService
from src.services.admin_panel_service import AdminPanelService
from src.services.task_management_service import TaskManagementService
from src.handlers.user_handlers import UserHandlers
from src.handlers.admin_handlers import AdminHandlers
from src.handlers.callback_handlers import CallbackHandlers
from src.utils.logger import setup_logger, log_user_action, log_admin_action
from src.utils.security import rate_limiter, ban_manager

logger = setup_logger(__name__)


class TelegramBot:
    """Main Telegram Bot Application"""
    
    def __init__(self):
        self.application = None
        self.database = None
        self.services = {}
        self.admin_sessions = {}
        
        # Initialize handlers
        self.user_handlers = None
        self.admin_handlers = None
        self.callback_handlers = None
        
    async def initialize_async_components(self):
        """Initialize async components"""
        try:
            logger.info("🤖 Starting Telegram Referral Bot...")
            logger.info("🔄 Mode: Long Polling")
            
            # Validate configuration
            Config.validate_config()
            logger.info("✅ Configuration validated")
            
            # Initialize database
            self.database = Database()
            await self.database.connect()
            logger.info("✅ Database connected")
            
            # Initialize services
            self.services = {
                'user': UserService(self.database),
                'referral': ReferralService(self.database),
                'transaction': TransactionService(self.database),
                'withdrawal': WithdrawalService(self.database),
                'channel': ChannelService(self.database),
                'product': ProductService(self.database),
                'admin_settings': AdminSettingsService(self.database.db),
                'admin_panel': AdminPanelService(self.database.db),
                'task_management': TaskManagementService(self.database.db, Config.BOT_TOKEN)
            }
            logger.info("✅ Services initialized")
            
            # Initialize handlers
            self.user_handlers = UserHandlers(self)
            self.admin_handlers = AdminHandlers(self)
            self.callback_handlers = CallbackHandlers(self)
            
            # Set cross-references for handlers
            self.callback_handlers.admin_handlers = self.admin_handlers
            self.callback_handlers.user_handlers = self.user_handlers
            
            # Initialize default products
            try:
                await self.services['product'].initialize_default_products()
                logger.info("✅ Default products initialized")
            except Exception as e:
                logger.warning(f"⚠️ Could not initialize default products: {e}")
                
        except Exception as e:
            logger.error(f"❌ Initialization failed: {e}")
            raise
    
    def get_main_keyboard(self):
        """Get main user menu keyboard"""
        keyboard = [
            [KeyboardButton("💰 Balance"), KeyboardButton("🎁 Daily Bonus")],
            [KeyboardButton("📋 Tasks"), KeyboardButton("👥 Referrals")],
            [KeyboardButton("💸 Withdraw")]
        ]
        return ReplyKeyboardMarkup(keyboard, resize_keyboard=True, one_time_keyboard=False)
    
    async def check_user_permissions(self, user_id: int) -> dict:
        """Check if user has permission to use the bot"""
        try:
            # Check if user is banned
            if await ban_manager.is_banned(user_id):
                return {
                    'allowed': False,
                    'reason': "❌ You are banned from using this bot."
                }
            
            # Check rate limiting
            if not await rate_limiter.check_rate_limit(user_id):
                return {
                    'allowed': False,
                    'reason': "⏳ Please wait before sending another request."
                }
            
            return {'allowed': True, 'reason': None}
            
        except Exception as e:
            logger.error(f"Error checking permissions: {e}")
            return {'allowed': True, 'reason': None}
    
    async def get_setting(self, setting_name: str, default_value):
        """Get a setting value with fallback to default"""
        try:
            return await self.services['admin_settings'].get_setting(setting_name, default_value)
        except:
            return default_value
    
    # ==================== COMMAND HANDLERS ====================
    
    async def start_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /start command with animated welcome sequence"""
        try:
            user = update.effective_user

            # Check user permissions
            permissions = await self.check_user_permissions(user.id)
            if not permissions['allowed']:
                await update.message.reply_text(permissions['reason'])
                return

            # Start the animated welcome sequence
            await self.animated_welcome_sequence(update, context, user)

        except Exception as e:
            logger.error(f"Error in start command: {e}")
            await update.message.reply_text("❌ An error occurred. Please try again.")
    
    async def animated_welcome_sequence(self, update, context, user):
        """Handle animated welcome sequence with user registration"""
        try:
            # Extract referral code from start parameter
            referral_code = None
            if context.args and len(context.args) > 0:
                referral_code = context.args[0]
            
            # Check if user exists
            db_user = await self.services['user'].get_user(user.id)
            
            if not db_user:
                # Register new user
                welcome_bonus = await self.get_setting('welcome_bonus', 0)
                db_user = await self.services['user'].create_user(
                    user_id=user.id,
                    username=user.username,
                    first_name=user.first_name,
                    last_name=user.last_name,
                    referral_code=referral_code,
                    welcome_bonus=welcome_bonus
                )
                
                # Process referral if applicable
                if referral_code and db_user:
                    try:
                        await self.services['referral'].process_referral(referral_code, user.id)
                    except Exception as e:
                        logger.error(f"Error processing referral: {e}")
                
                # Log user registration
                await log_user_action(user.id, 'USER_REGISTERED', f"New user registered with referral: {referral_code}")
            
            # Show welcome message with animation
            message = await update.message.reply_text("🎁 **Welcome!** Loading your profile...")
            
            # Animation frames
            frames = [
                "🎁 **Welcome!** Loading your profile...",
                "🎁 **Welcome!** Setting up your account... ⏳",
                "🎁 **Welcome!** Almost ready... ✨",
                "🎁 **Welcome!** Complete! 🎉"
            ]
            
            # Animate the welcome
            for frame in frames:
                await message.edit_text(frame, parse_mode='Markdown')
                await asyncio.sleep(0.5)
            
            # Show final welcome message
            welcome_content = self.get_final_welcome_message(user, db_user, referral_code)
            
            try:
                with open('start.jpg', 'rb') as photo_file:
                    await message.delete()
                    await update.message.reply_photo(
                        photo=photo_file,
                        caption=welcome_content,
                        parse_mode='Markdown',
                        reply_markup=self.get_main_keyboard()
                    )
            except:
                await message.edit_text(
                    welcome_content,
                    parse_mode='Markdown',
                    reply_markup=self.get_main_keyboard()
                )

        except Exception as e:
            logger.error(f"Error in animated welcome: {e}")
            # Fallback message
            fallback_message = "**_HELLO......WELCOME TO TITANIUM OFFICIAL GIFTS BOT💟_**\n\nUSE THE BOT NOW...😎👇"
            try:
                with open('start.jpg', 'rb') as photo_file:
                    await update.message.reply_photo(
                        photo=photo_file,
                        caption=fallback_message,
                        parse_mode='Markdown',
                        reply_markup=self.get_main_keyboard()
                    )
            except:
                await update.message.reply_text(
                    fallback_message,
                    parse_mode='Markdown',
                    reply_markup=self.get_main_keyboard()
                )
    
    def get_final_welcome_message(self, user, db_user, referral_code):
        """Generate branded welcome message"""
        user_first_name = user.first_name

        welcome_content = f"""
**_HELLO {user_first_name}......WELCOME TO TITANIUM OFFICIAL GIFTS BOT💟_**

use this bot to get 
- a lot of gifts from titanium channel 
- paid subscriptions of bots
- _pro of netflix, spotify, canva etc for FREE for 1 Year_

USE THE BOT NOW...😎👇
        """

        return welcome_content
    
    async def help_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /help command"""
        try:
            user_id = update.effective_user.id
            
            # Check permissions
            permissions = await self.check_user_permissions(user_id)
            if not permissions['allowed']:
                await update.message.reply_text(permissions['reason'])
                return
            
            help_text = f"""
❓ **HELP & SUPPORT** ❓

**How to use this bot:**

1. **Daily Bonus** - Claim 💎{Config.DAILY_BONUS_AMOUNT} every 24 hours
2. **Referrals** - Share your link to earn 💎{Config.REFERRAL_REWARD} per friend
3. **Tasks** - Complete tasks for extra rewards
4. **Withdraw** - Cash out at 💎{Config.MINIMUM_WITHDRAWAL} minimum

**Need help?** Contact support: @{Config.SUPPORT_USERNAME}
            """
            
            await update.message.reply_text(help_text, parse_mode='Markdown')
            
        except Exception as e:
            logger.error(f"Error in help command: {e}")
            await update.message.reply_text("❌ Failed to load help information.")
    
    async def balance_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle balance command"""
        try:
            user_id = update.effective_user.id

            # Check permissions
            permissions = await self.check_user_permissions(user_id)
            if not permissions['allowed']:
                await update.message.reply_text(permissions['reason'])
                return

            # Get user data
            user = await self.services['user'].get_user(user_id)
            if not user:
                await update.message.reply_text("❌ User not found. Please use /start first.")
                return

            # Get today's earnings
            today_earnings = await self.services['transaction'].get_today_earnings(user_id)

            balance_text = f"""
💰 **Your Balance**

💎 **Current Balance:** **💎{user.balance:.2f}**
📈 **Today's Earnings:** **💎{today_earnings:.2f}**
🌟 **Total Earned:** **💎{user.total_earned:.2f}**

👥 **Referrals:** **{user.successful_referrals}**
            """

            await update.message.reply_text(balance_text, parse_mode='Markdown')

        except Exception as e:
            logger.error(f"Error in balance command: {e}")
            await update.message.reply_text("❌ Failed to get balance information.")
    
    async def handle_message(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle text messages"""
        try:
            user_id = update.effective_user.id
            message_text = update.message.text

            # Check permissions
            permissions = await self.check_user_permissions(user_id)
            if not permissions['allowed']:
                await update.message.reply_text(permissions['reason'])
                return

            # Check for admin task creation workflow
            if user_id in Config.ADMIN_USER_IDS and 'admin_task_creation' in context.user_data:
                await self.admin_handlers.process_task_creation_step(update, context, message_text)
                return

            # Check for image submission workflow
            if 'image_submission' in context.user_data:
                if update.message.photo:
                    await self.user_handlers.process_image_submission(update, context)
                else:
                    await update.message.reply_text(
                        "❌ Please send an image file for task submission.\n\n"
                        "You can also include text along with the image if needed."
                    )
                return

            # Handle main menu buttons
            if message_text == "💰 Balance":
                await self.balance_command(update, context)
            elif message_text == "🎁 Daily Bonus":
                await self.user_handlers.handle_daily_bonus(update, context)
            elif message_text == "📋 Tasks":
                await self.user_handlers.handle_tasks_menu(update, context)
            elif message_text == "👥 Referrals":
                await self.user_handlers.handle_referrals_menu(update, context)
            elif message_text == "💸 Withdraw":
                await update.message.reply_text("💸 **Withdraw**\n\n🚧 Withdrawal features coming soon...")
            else:
                await update.message.reply_text(
                    "🎁 Use the buttons below to get amazing gifts! ✨",
                    reply_markup=self.get_main_keyboard()
                )

        except Exception as e:
            logger.error(f"Error handling message: {e}")
            await update.message.reply_text("❌ An error occurred. Please try again.")
    
    async def error_handler(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle errors"""
        logger.error(f"Update {update} caused error {context.error}")


def main():
    """Main function to run the bot"""
    try:
        logger.info("🤖 Starting Telegram Referral Bot...")
        
        # Validate configuration
        Config.validate_config()
        logger.info("✅ Configuration validated")

        # Create bot instance
        bot = TelegramBot()

        # Create application
        application = Application.builder().token(Config.BOT_TOKEN).build()
        bot.application = application

        # Initialize and run
        async def init_and_run():
            await bot.initialize_async_components()

            # Add handlers after initialization
            application.add_handler(CommandHandler("start", bot.start_command))
            application.add_handler(CommandHandler("help", bot.help_command))
            application.add_handler(CommandHandler("balance", bot.balance_command))
            application.add_handler(CommandHandler("admin", bot.admin_handlers.admin_panel_command))

            application.add_handler(MessageHandler(filters.TEXT & ~filters.COMMAND, bot.handle_message))
            application.add_handler(CallbackQueryHandler(bot.callback_handlers.handle_callback))
            application.add_error_handler(bot.error_handler)

            logger.info("✅ All handlers added")
            logger.info("🚀 Bot is running! Press Ctrl+C to stop.")

        # Run the bot
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        loop.run_until_complete(init_and_run())

        application.run_polling(
            allowed_updates=Update.ALL_TYPES,
            drop_pending_updates=True
        )

    except KeyboardInterrupt:
        logger.info("👋 Bot stopped by user")
    except Exception as e:
        logger.error(f"❌ Bot error: {e}")
        raise


if __name__ == "__main__":
    main()
