import logging
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime, timezone, timedelta
from motor.motor_asyncio import AsyncIOMotorDatabase

from ..models.user import User
from ..models.transaction import Transaction, TransactionType
from ..models.admin_settings import AdminSettings

logger = logging.getLogger(__name__)

class AdminPanelService:
    """Service for comprehensive admin panel functionality"""
    
    def __init__(self, db: AsyncIOMotorDatabase):
        self.db = db
        self.users_collection = db.users
        self.transactions_collection = db.transactions
        self.admin_logs_collection = db.admin_logs
        self.broadcasts_collection = db.broadcasts
        self.tasks_collection = db.admin_tasks
    
    # ==================== USER MANAGEMENT ====================
    
    async def get_users_paginated(self, page: int = 1, per_page: int = 10) -> Tuple[List[User], int, int]:
        """Get paginated list of users"""
        try:
            skip = (page - 1) * per_page
            
            # Get total count
            total_users = await self.users_collection.count_documents({})
            total_pages = (total_users + per_page - 1) // per_page
            
            # Get users for current page
            cursor = self.users_collection.find({}).sort('created_at', -1).skip(skip).limit(per_page)
            users = []
            
            async for user_data in cursor:
                users.append(User.from_dict(user_data))
            
            return users, total_pages, total_users
            
        except Exception as e:
            logger.error(f"Failed to get paginated users: {e}")
            return [], 0, 0
    
    async def search_users(self, query: str, page: int = 1, per_page: int = 10) -> Tuple[List[User], int]:
        """Search users by ID, username, or name"""
        try:
            # Build search filters
            search_filters = []
            
            if query.isdigit():
                search_filters.append({'user_id': int(query)})
            
            search_filters.extend([
                {'username': {'$regex': query, '$options': 'i'}},
                {'first_name': {'$regex': query, '$options': 'i'}},
                {'last_name': {'$regex': query, '$options': 'i'}}
            ])
            
            search_query = {'$or': search_filters}
            
            # Get total count
            total_results = await self.users_collection.count_documents(search_query)
            
            # Get results for current page
            skip = (page - 1) * per_page
            cursor = self.users_collection.find(search_query).sort('created_at', -1).skip(skip).limit(per_page)
            users = []
            
            async for user_data in cursor:
                users.append(User.from_dict(user_data))
            
            return users, total_results
            
        except Exception as e:
            logger.error(f"Failed to search users: {e}")
            return [], 0
    
    async def get_user_detailed_info(self, user_id: int) -> Optional[Dict[str, Any]]:
        """Get detailed user information for admin view"""
        try:
            # Get user data
            user_data = await self.users_collection.find_one({'user_id': user_id})
            if not user_data:
                return None
            
            user = User.from_dict(user_data)
            
            # Get transaction history
            transactions_cursor = self.transactions_collection.find(
                {'user_id': user_id}
            ).sort('created_at', -1).limit(10)
            
            transactions = []
            async for trans_data in transactions_cursor:
                transactions.append(Transaction.from_dict(trans_data))
            
            # Get referral statistics
            referrals_count = await self.users_collection.count_documents({'referred_by': user_id})
            
            # Calculate today's activity
            today_start = datetime.now(timezone.utc).replace(hour=0, minute=0, second=0, microsecond=0)
            today_transactions = await self.transactions_collection.count_documents({
                'user_id': user_id,
                'created_at': {'$gte': today_start.isoformat()}
            })
            
            return {
                'user': user,
                'transactions': transactions,
                'referrals_count': referrals_count,
                'today_activity': today_transactions,
                'account_age_days': (datetime.now(timezone.utc) - user.created_at).days if user.created_at else 0
            }
            
        except Exception as e:
            logger.error(f"Failed to get detailed user info for {user_id}: {e}")
            return None
    
    async def modify_user_balance(self, admin_id: int, user_id: int, amount: float, reason: str, operation: str = 'add') -> bool:
        """Modify user balance with admin tracking"""
        try:
            user_data = await self.users_collection.find_one({'user_id': user_id})
            if not user_data:
                return False
            
            user = User.from_dict(user_data)
            old_balance = user.balance
            
            if operation == 'add':
                user.add_balance(amount, f"Admin adjustment: {reason}")
            elif operation == 'subtract':
                user.balance = max(0, user.balance - amount)
                user.update_activity()
            elif operation == 'set':
                user.balance = amount
                user.update_activity()
            
            # Update user in database
            await self.users_collection.update_one(
                {'user_id': user_id},
                {'$set': user.to_dict()}
            )
            
            # Create transaction record
            transaction = Transaction(
                user_id=user_id,
                amount=amount if operation == 'add' else -amount,
                transaction_type=TransactionType.ADMIN_ADJUSTMENT,
                description=f"Admin {operation}: {reason}",
                created_at=datetime.now(timezone.utc)
            )
            
            await self.transactions_collection.insert_one(transaction.to_dict())
            
            # Log admin action
            await self.log_admin_action(
                admin_id, 
                'BALANCE_MODIFIED',
                f"User {user_id}: {operation} 💎{amount} - {reason} (Old: 💎{old_balance}, New: 💎{user.balance})"
            )
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to modify user balance: {e}")
            return False
    
    async def ban_user(self, admin_id: int, user_id: int, reason: str, duration_hours: Optional[int] = None) -> bool:
        """Ban user with reason and optional duration"""
        try:
            ban_until = None
            if duration_hours:
                ban_until = datetime.now(timezone.utc) + timedelta(hours=duration_hours)
            
            result = await self.users_collection.update_one(
                {'user_id': user_id},
                {
                    '$set': {
                        'is_banned': True,
                        'ban_reason': reason,
                        'banned_until': ban_until.isoformat() if ban_until else None,
                        'banned_by': admin_id,
                        'banned_at': datetime.now(timezone.utc).isoformat()
                    }
                }
            )
            
            if result.modified_count > 0:
                duration_text = f" for {duration_hours} hours" if duration_hours else " permanently"
                await self.log_admin_action(
                    admin_id,
                    'USER_BANNED',
                    f"User {user_id} banned{duration_text} - Reason: {reason}"
                )
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"Failed to ban user {user_id}: {e}")
            return False
    
    async def unban_user(self, admin_id: int, user_id: int) -> bool:
        """Unban user"""
        try:
            result = await self.users_collection.update_one(
                {'user_id': user_id},
                {
                    '$set': {
                        'is_banned': False,
                        'ban_reason': None,
                        'banned_until': None,
                        'unbanned_by': admin_id,
                        'unbanned_at': datetime.now(timezone.utc).isoformat()
                    }
                }
            )
            
            if result.modified_count > 0:
                await self.log_admin_action(admin_id, 'USER_UNBANNED', f"User {user_id} unbanned")
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"Failed to unban user {user_id}: {e}")
            return False
    
    async def delete_user(self, admin_id: int, user_id: int) -> bool:
        """Delete user account completely"""
        try:
            # Delete user transactions
            await self.transactions_collection.delete_many({'user_id': user_id})
            
            # Delete user record
            result = await self.users_collection.delete_one({'user_id': user_id})
            
            if result.deleted_count > 0:
                await self.log_admin_action(admin_id, 'USER_DELETED', f"User {user_id} account deleted")
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"Failed to delete user {user_id}: {e}")
            return False
    
    # ==================== ADMIN LOGGING ====================
    
    async def log_admin_action(self, admin_id: int, action: str, details: str):
        """Log admin action for audit trail"""
        try:
            log_entry = {
                'admin_id': admin_id,
                'action': action,
                'details': details,
                'timestamp': datetime.now(timezone.utc).isoformat(),
                'ip_address': None  # Could be added later
            }
            
            await self.admin_logs_collection.insert_one(log_entry)
            logger.info(f"Admin {admin_id} performed {action}: {details}")
            
        except Exception as e:
            logger.error(f"Failed to log admin action: {e}")
    
    async def get_admin_logs(self, page: int = 1, per_page: int = 20) -> Tuple[List[Dict], int]:
        """Get paginated admin logs"""
        try:
            skip = (page - 1) * per_page
            
            total_logs = await self.admin_logs_collection.count_documents({})
            
            cursor = self.admin_logs_collection.find({}).sort('timestamp', -1).skip(skip).limit(per_page)
            logs = []
            
            async for log_data in cursor:
                log_data.pop('_id', None)
                logs.append(log_data)
            
            return logs, total_logs
            
        except Exception as e:
            logger.error(f"Failed to get admin logs: {e}")
            return [], 0

    # ==================== BROADCASTING SYSTEM ====================

    async def create_broadcast(self, admin_id: int, message: str, target_group: str = 'all',
                             scheduled_for: Optional[datetime] = None) -> str:
        """Create a new broadcast message"""
        try:
            broadcast_id = f"broadcast_{int(datetime.now().timestamp())}"

            broadcast_data = {
                'broadcast_id': broadcast_id,
                'admin_id': admin_id,
                'message': message,
                'target_group': target_group,
                'scheduled_for': scheduled_for.isoformat() if scheduled_for else None,
                'created_at': datetime.now(timezone.utc).isoformat(),
                'status': 'scheduled' if scheduled_for else 'pending',
                'sent_count': 0,
                'delivered_count': 0,
                'failed_count': 0
            }

            await self.broadcasts_collection.insert_one(broadcast_data)

            await self.log_admin_action(
                admin_id,
                'BROADCAST_CREATED',
                f"Broadcast {broadcast_id} created for {target_group}"
            )

            return broadcast_id

        except Exception as e:
            logger.error(f"Failed to create broadcast: {e}")
            return ""

    async def get_target_users(self, target_group: str) -> List[int]:
        """Get user IDs based on target group"""
        try:
            query = {}

            if target_group == 'all':
                query = {'is_banned': {'$ne': True}}
            elif target_group == 'active':
                # Users active in last 7 days
                week_ago = datetime.now(timezone.utc) - timedelta(days=7)
                query = {
                    'is_banned': {'$ne': True},
                    'last_activity': {'$gte': week_ago.isoformat()}
                }
            elif target_group == 'premium':
                query = {
                    'is_banned': {'$ne': True},
                    'balance': {'$gte': 500}  # Users with high balance
                }
            elif target_group == 'new':
                # Users joined in last 3 days
                three_days_ago = datetime.now(timezone.utc) - timedelta(days=3)
                query = {
                    'is_banned': {'$ne': True},
                    'created_at': {'$gte': three_days_ago.isoformat()}
                }

            cursor = self.users_collection.find(query, {'user_id': 1})
            user_ids = []

            async for user_data in cursor:
                user_ids.append(user_data['user_id'])

            return user_ids

        except Exception as e:
            logger.error(f"Failed to get target users for {target_group}: {e}")
            return []

    async def update_broadcast_stats(self, broadcast_id: str, sent: int = 0, delivered: int = 0, failed: int = 0):
        """Update broadcast delivery statistics"""
        try:
            await self.broadcasts_collection.update_one(
                {'broadcast_id': broadcast_id},
                {
                    '$inc': {
                        'sent_count': sent,
                        'delivered_count': delivered,
                        'failed_count': failed
                    },
                    '$set': {
                        'last_updated': datetime.now(timezone.utc).isoformat()
                    }
                }
            )

        except Exception as e:
            logger.error(f"Failed to update broadcast stats: {e}")

    async def get_broadcast_history(self, page: int = 1, per_page: int = 10) -> Tuple[List[Dict], int]:
        """Get paginated broadcast history"""
        try:
            skip = (page - 1) * per_page

            total_broadcasts = await self.broadcasts_collection.count_documents({})

            cursor = self.broadcasts_collection.find({}).sort('created_at', -1).skip(skip).limit(per_page)
            broadcasts = []

            async for broadcast_data in cursor:
                broadcast_data.pop('_id', None)
                broadcasts.append(broadcast_data)

            return broadcasts, total_broadcasts

        except Exception as e:
            logger.error(f"Failed to get broadcast history: {e}")
            return [], 0

    # ==================== ANALYTICS AND REPORTING ====================

    async def get_bot_statistics(self) -> Dict[str, Any]:
        """Get comprehensive bot statistics"""
        try:
            # User statistics
            total_users = await self.users_collection.count_documents({})
            active_users = await self.users_collection.count_documents({
                'last_activity': {'$gte': (datetime.now(timezone.utc) - timedelta(days=7)).isoformat()}
            })
            banned_users = await self.users_collection.count_documents({'is_banned': True})

            # Financial statistics
            total_balance_pipeline = [
                {'$group': {'_id': None, 'total': {'$sum': '$balance'}}}
            ]
            balance_result = await self.users_collection.aggregate(total_balance_pipeline).to_list(1)
            total_balance = balance_result[0]['total'] if balance_result else 0

            total_earned_pipeline = [
                {'$group': {'_id': None, 'total': {'$sum': '$total_earned'}}}
            ]
            earned_result = await self.users_collection.aggregate(total_earned_pipeline).to_list(1)
            total_earned = earned_result[0]['total'] if earned_result else 0

            # Transaction statistics
            today_start = datetime.now(timezone.utc).replace(hour=0, minute=0, second=0, microsecond=0)
            today_transactions = await self.transactions_collection.count_documents({
                'created_at': {'$gte': today_start.isoformat()}
            })

            # Referral statistics
            total_referrals_pipeline = [
                {'$group': {'_id': None, 'total': {'$sum': '$successful_referrals'}}}
            ]
            referrals_result = await self.users_collection.aggregate(total_referrals_pipeline).to_list(1)
            total_referrals = referrals_result[0]['total'] if referrals_result else 0

            return {
                'users': {
                    'total': total_users,
                    'active_7_days': active_users,
                    'banned': banned_users,
                    'active_percentage': round((active_users / total_users * 100) if total_users > 0 else 0, 2)
                },
                'financial': {
                    'total_balance': round(total_balance, 2),
                    'total_earned': round(total_earned, 2),
                    'average_balance': round(total_balance / total_users, 2) if total_users > 0 else 0
                },
                'activity': {
                    'today_transactions': today_transactions,
                    'total_referrals': total_referrals
                },
                'generated_at': datetime.now(timezone.utc).isoformat()
            }

        except Exception as e:
            logger.error(f"Failed to get bot statistics: {e}")
            return {}

    async def get_user_activity_report(self, days: int = 7) -> Dict[str, Any]:
        """Get user activity report for specified days"""
        try:
            start_date = datetime.now(timezone.utc) - timedelta(days=days)

            # Daily user registrations
            registration_pipeline = [
                {
                    '$match': {
                        'created_at': {'$gte': start_date.isoformat()}
                    }
                },
                {
                    '$group': {
                        '_id': {'$dateToString': {'format': '%Y-%m-%d', 'date': {'$dateFromString': {'dateString': '$created_at'}}}},
                        'count': {'$sum': 1}
                    }
                },
                {'$sort': {'_id': 1}}
            ]

            registrations = await self.users_collection.aggregate(registration_pipeline).to_list(days)

            # Daily transaction activity
            transaction_pipeline = [
                {
                    '$match': {
                        'created_at': {'$gte': start_date.isoformat()}
                    }
                },
                {
                    '$group': {
                        '_id': {'$dateToString': {'format': '%Y-%m-%d', 'date': {'$dateFromString': {'dateString': '$created_at'}}}},
                        'count': {'$sum': 1},
                        'total_amount': {'$sum': '$amount'}
                    }
                },
                {'$sort': {'_id': 1}}
            ]

            transactions = await self.transactions_collection.aggregate(transaction_pipeline).to_list(days)

            return {
                'period_days': days,
                'registrations': registrations,
                'transactions': transactions,
                'generated_at': datetime.now(timezone.utc).isoformat()
            }

        except Exception as e:
            logger.error(f"Failed to get user activity report: {e}")
            return {}

    async def get_top_referrers(self, limit: int = 10) -> List[Dict[str, Any]]:
        """Get top referrers by successful referrals"""
        try:
            pipeline = [
                {'$match': {'successful_referrals': {'$gt': 0}}},
                {'$sort': {'successful_referrals': -1}},
                {'$limit': limit},
                {
                    '$project': {
                        'user_id': 1,
                        'first_name': 1,
                        'username': 1,
                        'successful_referrals': 1,
                        'total_earned': 1,
                        'balance': 1
                    }
                }
            ]

            top_referrers = await self.users_collection.aggregate(pipeline).to_list(limit)

            # Remove MongoDB _id field
            for referrer in top_referrers:
                referrer.pop('_id', None)

            return top_referrers

        except Exception as e:
            logger.error(f"Failed to get top referrers: {e}")
            return []

    # ==================== TASK MANAGEMENT ====================

    async def create_admin_task(self, admin_id: int, title: str, description: str,
                               reward: float, task_type: str, requirement: str) -> str:
        """Create a new admin-managed task"""
        try:
            task_id = f"task_{int(datetime.now().timestamp())}"

            task_data = {
                'task_id': task_id,
                'title': title,
                'description': description,
                'reward': reward,
                'task_type': task_type,
                'requirement': requirement,
                'is_active': True,
                'created_by': admin_id,
                'created_at': datetime.now(timezone.utc).isoformat(),
                'completion_count': 0
            }

            await self.tasks_collection.insert_one(task_data)

            await self.log_admin_action(
                admin_id,
                'TASK_CREATED',
                f"Task '{title}' created with 💎{reward} reward"
            )

            return task_id

        except Exception as e:
            logger.error(f"Failed to create admin task: {e}")
            return ""

    async def get_admin_tasks(self, include_inactive: bool = False) -> List[Dict[str, Any]]:
        """Get all admin-managed tasks"""
        try:
            query = {} if include_inactive else {'is_active': True}

            cursor = self.tasks_collection.find(query).sort('created_at', -1)
            tasks = []

            async for task_data in cursor:
                task_data.pop('_id', None)
                tasks.append(task_data)

            return tasks

        except Exception as e:
            logger.error(f"Failed to get admin tasks: {e}")
            return []

    async def update_admin_task(self, admin_id: int, task_id: str, updates: Dict[str, Any]) -> bool:
        """Update an admin task"""
        try:
            # Add update tracking
            updates['updated_by'] = admin_id
            updates['updated_at'] = datetime.now(timezone.utc).isoformat()

            result = await self.tasks_collection.update_one(
                {'task_id': task_id},
                {'$set': updates}
            )

            if result.modified_count > 0:
                await self.log_admin_action(
                    admin_id,
                    'TASK_UPDATED',
                    f"Task {task_id} updated: {list(updates.keys())}"
                )
                return True

            return False

        except Exception as e:
            logger.error(f"Failed to update admin task: {e}")
            return False

    async def delete_admin_task(self, admin_id: int, task_id: str) -> bool:
        """Delete an admin task"""
        try:
            result = await self.tasks_collection.delete_one({'task_id': task_id})

            if result.deleted_count > 0:
                await self.log_admin_action(
                    admin_id,
                    'TASK_DELETED',
                    f"Task {task_id} deleted"
                )
                return True

            return False

        except Exception as e:
            logger.error(f"Failed to delete admin task: {e}")
            return False

    async def get_task_statistics(self) -> Dict[str, Any]:
        """Get task completion statistics"""
        try:
            # Total tasks
            total_tasks = await self.tasks_collection.count_documents({})
            active_tasks = await self.tasks_collection.count_documents({'is_active': True})

            # Task completion stats
            completion_pipeline = [
                {
                    '$group': {
                        '_id': None,
                        'total_completions': {'$sum': '$completion_count'},
                        'total_rewards': {'$sum': {'$multiply': ['$completion_count', '$reward']}}
                    }
                }
            ]

            completion_result = await self.tasks_collection.aggregate(completion_pipeline).to_list(1)

            if completion_result:
                total_completions = completion_result[0]['total_completions']
                total_rewards = completion_result[0]['total_rewards']
            else:
                total_completions = 0
                total_rewards = 0

            # Most popular tasks
            popular_tasks = await self.tasks_collection.find(
                {'completion_count': {'$gt': 0}}
            ).sort('completion_count', -1).limit(5).to_list(5)

            for task in popular_tasks:
                task.pop('_id', None)

            return {
                'total_tasks': total_tasks,
                'active_tasks': active_tasks,
                'total_completions': total_completions,
                'total_rewards_distributed': round(total_rewards, 2),
                'popular_tasks': popular_tasks,
                'generated_at': datetime.now(timezone.utc).isoformat()
            }

        except Exception as e:
            logger.error(f"Failed to get task statistics: {e}")
            return {}

    # ==================== UTILITY METHODS ====================

    async def validate_admin_access(self, user_id: int, admin_ids: List[int]) -> bool:
        """Validate if user has admin access"""
        return user_id in admin_ids

    async def get_system_health(self) -> Dict[str, Any]:
        """Get system health metrics"""
        try:
            # Database connection test
            db_healthy = True
            try:
                await self.db.command('ping')
            except:
                db_healthy = False

            # Collection sizes
            users_count = await self.users_collection.count_documents({})
            transactions_count = await self.transactions_collection.count_documents({})
            logs_count = await self.admin_logs_collection.count_documents({})

            return {
                'database_healthy': db_healthy,
                'collections': {
                    'users': users_count,
                    'transactions': transactions_count,
                    'admin_logs': logs_count
                },
                'timestamp': datetime.now(timezone.utc).isoformat()
            }

        except Exception as e:
            logger.error(f"Failed to get system health: {e}")
            return {'database_healthy': False, 'error': str(e)}
