"""
Callback query handlers for the Telegram bot
"""

from telegram import Update
from telegram.ext import ContextTypes

from config import Config
from src.utils.logger import setup_logger

logger = setup_logger(__name__)


class CallbackHandlers:
    """Handles callback query interactions"""
    
    def __init__(self, bot_instance):
        self.bot = bot_instance
        self.services = bot_instance.services
        self.admin_handlers = None  # Will be set by main bot
        self.user_handlers = None   # Will be set by main bot
    
    async def handle_callback(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Main callback query handler"""
        try:
            query = update.callback_query
            await query.answer()
            
            user_id = query.from_user.id
            data = query.data
            
            # Check user permissions
            permissions = await self.bot.check_user_permissions(user_id)
            if not permissions['allowed']:
                await query.edit_message_text(permissions['reason'])
                return
            
            # Route callback based on data
            if data.startswith("admin_"):
                # Check admin access for admin callbacks
                if not await self.services['admin_panel'].validate_admin_access(user_id, Config.ADMIN_USER_IDS):
                    await query.edit_message_text("❌ Access denied. Admin privileges required.")
                    return
                await self.handle_admin_callbacks(query, context, data)
            elif data.startswith("balance_"):
                await self.handle_balance_callbacks(query, context, data)
            elif data.startswith("copy_referral_link"):
                await self.copy_referral_link(query, context)
            elif data.startswith("withdraw_"):
                await self.handle_withdrawal_callbacks(query, context, data)
            elif data.startswith("settings_"):
                await self.handle_settings_callbacks(query, context, data)
            else:
                await query.edit_message_text("🚧 Feature coming soon...")
                
        except Exception as e:
            logger.error(f"Error in callback handler: {e}")
            try:
                await query.edit_message_text("❌ An error occurred. Please try again.")
            except:
                pass
    
    async def handle_admin_callbacks(self, query, context, data):
        """Handle admin panel callbacks"""
        try:
            user_id = query.from_user.id

            # Main menu navigation
            if data == "admin_main":
                await self.admin_handlers.show_admin_main_menu_callback(query, context)
            elif data == "admin_users":
                await self.admin_handlers.show_user_management_menu_callback(query, context)
            elif data == "admin_settings_menu":
                await self.admin_handlers.show_bot_settings_menu_callback(query, context)
            elif data == "admin_tasks":
                await self.show_task_management_menu(query, context)
            elif data == "admin_broadcast":
                await self.show_broadcast_menu(query, context)
            elif data == "admin_analytics":
                await self.show_analytics_menu(query, context)
            elif data == "admin_health":
                await self.show_system_health(query, context)
            elif data == "admin_logs":
                await self.show_admin_logs(query, context)
            
            # User management callbacks
            elif data == "admin_users_list":
                await self.show_users_list(query, context)
            elif data == "admin_user_reports":
                await self.show_user_reports(query, context)
            elif data == "admin_search_user":
                await query.edit_message_text("🔍 **Search User**\n\nSend user ID or username to search:")
            elif data == "admin_manage_balances":
                await query.edit_message_text("💰 **Manage Balances**\n\nFeature coming soon...")
            elif data == "admin_ban_users":
                await query.edit_message_text("🚫 **Ban/Unban Users**\n\nFeature coming soon...")
            elif data == "admin_message_user":
                await query.edit_message_text("📢 **Message User**\n\nFeature coming soon...")
            
            # Settings callbacks
            elif data == "admin_set_referral":
                await self.initiate_setting_change(query, context, 'referral')
            elif data == "admin_set_daily":
                await self.initiate_setting_change(query, context, 'daily')
            elif data == "admin_set_withdrawal":
                await self.initiate_setting_change(query, context, 'withdrawal')
            elif data == "admin_set_cooldown":
                await self.initiate_setting_change(query, context, 'cooldown')
            elif data == "admin_set_welcome":
                await self.initiate_setting_change(query, context, 'welcome')
            elif data == "admin_set_limits":
                await query.edit_message_text("🔢 **Daily Limits**\n\nFeature coming soon...")
            elif data == "admin_reset_settings":
                await query.edit_message_text("🔄 **Reset Settings**\n\nFeature coming soon...")
            elif data == "admin_export_settings":
                await query.edit_message_text("💾 **Export Settings**\n\nFeature coming soon...")
            
            else:
                await query.edit_message_text("🚧 Admin feature coming soon...")
                
        except Exception as e:
            logger.error(f"Error in admin callbacks: {e}")
            await query.answer("❌ Failed to process admin action.")
    
    async def handle_balance_callbacks(self, query, context, data):
        """Handle balance-related callbacks"""
        try:
            user_id = query.from_user.id
            action = data.replace("balance_", "")

            if action == "refresh":
                await self.refresh_balance(query, context)

        except Exception as e:
            logger.error(f"Error in balance callback: {e}")
    
    async def refresh_balance(self, query, context):
        """Refresh and show current balance"""
        try:
            user_id = query.from_user.id
            
            # Get user data
            user = await self.services['user'].get_user(user_id)
            if not user:
                await query.edit_message_text("❌ User not found. Please use /start first.")
                return
            
            # Get today's earnings
            today_earnings = await self.services['transaction'].get_today_earnings(user_id)
            
            # Determine withdrawal status
            if user.balance >= Config.MINIMUM_WITHDRAWAL:
                status_icon = "✅"
                status_text = "Ready for withdrawal!"
            else:
                status_icon = "📈"
                needed = Config.MINIMUM_WITHDRAWAL - user.balance
                status_text = f"Need 💎{needed:.2f} more"
            
            balance_text = f"""
💰 **Your Balance** {status_icon}

💎 **Current Balance:** **💎{user.balance:.2f}**
📈 **Today's Earnings:** **💎{today_earnings:.2f}**
🌟 **Total Earned:** **💎{user.total_earned:.2f}**

👥 **Referrals:** **{user.successful_referrals}** (*💎{user.successful_referrals * Config.REFERRAL_REWARD:.2f} earned*)

*{status_text}*
            """
            
            await query.edit_message_text(
                balance_text,
                parse_mode='Markdown'
            )
            
        except Exception as e:
            logger.error(f"Error refreshing balance: {e}")
    
    async def copy_referral_link(self, query, context):
        """Show referral link for copying"""
        try:
            user_id = query.from_user.id
            
            # Get user
            user = await self.services['user'].get_user(user_id)
            if not user:
                await query.edit_message_text("❌ User not found. Please use /start first.")
                return
            
            referral_link = f"https://t.me/{Config.BOT_USERNAME}?start={user.referral_code}"
            
            link_text = f"""
📋 **Your Referral Link**

`{referral_link}`

*Tap to copy the link above and share with friends!*

💰 **Earn 💎{Config.REFERRAL_REWARD}** for each friend who joins!
            """
            
            await query.edit_message_text(
                link_text,
                parse_mode='Markdown'
            )
            
        except Exception as e:
            logger.error(f"Error copying referral link: {e}")
    
    async def handle_withdrawal_callbacks(self, query, context, data):
        """Handle withdrawal-related callbacks"""
        await query.edit_message_text("🚧 Withdrawal features coming soon...")
    
    async def handle_settings_callbacks(self, query, context, data):
        """Handle settings-related callbacks"""
        await query.edit_message_text("🚧 Settings features coming soon...")
    
    async def initiate_setting_change(self, query, context, setting_type):
        """Initiate admin setting change process"""
        try:
            settings = await self.services['admin_settings'].get_all_settings()
            
            setting_prompts = {
                'referral': "💰 **Set Referral Reward**\n\nCurrent: 💎{}\n\nPlease send the new referral reward amount (💎1-💎1000):",
                'daily': "🎁 **Set Daily Bonus**\n\nCurrent: 💎{}\n\nPlease send the new daily bonus amount (💎1-💎100):",
                'withdrawal': "💸 **Set Minimum Withdrawal**\n\nCurrent: 💎{}\n\nPlease send the new minimum withdrawal amount (💎100-💎10000):",
                'cooldown': "⏰ **Set Withdrawal Cooldown**\n\nCurrent: {}h\n\nPlease send the new cooldown in hours (1-168):",
                'welcome': "🎊 **Set Welcome Bonus**\n\nCurrent: 💎{}\n\nPlease send the new welcome bonus amount (💎0-💎100):",
            }
            
            if setting_type in setting_prompts:
                current_value = getattr(settings, {
                    'referral': 'referral_reward',
                    'daily': 'daily_bonus_amount', 
                    'withdrawal': 'minimum_withdrawal',
                    'cooldown': 'withdrawal_cooldown_hours',
                    'welcome': 'welcome_bonus'
                }[setting_type])
                
                prompt = setting_prompts[setting_type].format(current_value)
                
                # Store the setting type in user data
                context.user_data['admin_setting_change'] = setting_type
                
                await query.edit_message_text(prompt, parse_mode='Markdown')
            else:
                await query.edit_message_text("❌ Invalid setting type.")
                
        except Exception as e:
            logger.error(f"Error initiating setting change: {e}")
            await query.answer("❌ Failed to initiate setting change.")
    
    # Placeholder methods for features to be implemented
    async def show_task_management_menu(self, query, context):
        """Show task management menu"""
        await query.edit_message_text("📋 **Task Management**\n\nFeature coming soon...")
    
    async def show_broadcast_menu(self, query, context):
        """Show broadcasting menu"""
        await query.edit_message_text("📢 **Broadcasting**\n\nFeature coming soon...")
    
    async def show_analytics_menu(self, query, context):
        """Show analytics menu"""
        await query.edit_message_text("📊 **Analytics**\n\nFeature coming soon...")
    
    async def show_system_health(self, query, context):
        """Show system health"""
        await query.edit_message_text("🔒 **System Health**\n\nFeature coming soon...")
    
    async def show_admin_logs(self, query, context):
        """Show admin logs"""
        await query.edit_message_text("📋 **Admin Logs**\n\nFeature coming soon...")
    
    async def show_users_list(self, query, context):
        """Show users list"""
        await query.edit_message_text("📋 **Users List**\n\nFeature coming soon...")
    
    async def show_user_reports(self, query, context):
        """Show user reports"""
        await query.edit_message_text("📊 **User Reports**\n\nFeature coming soon...")
