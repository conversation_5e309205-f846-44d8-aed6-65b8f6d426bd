"""
User command handlers for the Telegram bot
"""

import asyncio
import pytz
from datetime import datetime, timezone
from telegram import Update, InlineKeyboardMarkup, InlineKeyboardButton
from telegram.ext import ContextTypes

from config import Config
from src.models.transaction import TransactionType
from src.utils.logger import setup_logger, log_user_action

logger = setup_logger(__name__)


class UserHandlers:
    """Handles user-related commands and interactions"""
    
    def __init__(self, bot_instance):
        self.bot = bot_instance
        self.services = bot_instance.services
    
    async def handle_daily_bonus(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle daily bonus claiming with enhanced 2-3 second animation"""
        try:
            user_id = update.effective_user.id
            
            # Check permissions
            permissions = await self.bot.check_user_permissions(user_id)
            if not permissions['allowed']:
                await update.message.reply_text(permissions['reason'])
                return
            
            # Get user
            user = await self.services['user'].get_user(user_id)
            if not user:
                await update.message.reply_text("❌ User not found. Please use /start first.")
                return
            
            # Check if daily bonus can be claimed
            can_claim, time_left = await self.services['user'].can_claim_daily_bonus(user_id)
            
            if not can_claim:
                hours = int(time_left // 3600)
                minutes = int((time_left % 3600) // 60)
                
                cooldown_text = f"""
🕐 **Daily Bonus Cooldown**

⏰ **Next claim in:** {hours}h {minutes}m
💎 **Daily gift:** 💎{Config.DAILY_BONUS_AMOUNT}

*Come back later for your daily reward!*
                """
                
                keyboard = [[InlineKeyboardButton("🔄 Refresh", callback_data="balance_refresh")]]
                reply_markup = InlineKeyboardMarkup(keyboard)
                
                await update.message.reply_text(
                    cooldown_text,
                    parse_mode='Markdown',
                    reply_markup=reply_markup
                )
                return
            
            # Start animated claiming sequence
            message = await update.message.reply_text("🎁 **Claiming daily bonus...**")
            
            # Animation frames
            frames = [
                "🎁 **Claiming daily bonus...**",
                "🎁 **Claiming daily bonus...** ⏳",
                "🎁 **Processing reward...** 💫",
                "🎁 **Almost ready...** ✨",
                "🎁 **Success!** 🎉"
            ]
            
            # Animate the claiming process
            for frame in frames:
                await message.edit_text(frame, parse_mode='Markdown')
                await asyncio.sleep(0.4)  # 400ms delay between frames
            
            # Claim the bonus
            daily_bonus_amount = await self.bot.get_setting('daily_bonus_amount', Config.DAILY_BONUS_AMOUNT)
            success = await self.services['user'].claim_daily_bonus(user_id, daily_bonus_amount)
            
            if success:
                # Update user balance
                user = await self.services['user'].get_user(user_id)
                
                # Log the action
                await log_user_action(user_id, 'DAILY_BONUS_CLAIMED', f"Claimed daily bonus: 💎{daily_bonus_amount}")
                
                # Create transaction record
                try:
                    await self.services['transaction'].create_transaction(
                        user_id=user_id,
                        amount=daily_bonus_amount,
                        transaction_type=TransactionType.DAILY_BONUS,
                        description="Daily bonus claimed"
                    )
                except Exception as e:
                    logger.error(f"Failed to create transaction: {e}")
                
                # Concise success message
                bonus_text = f"""
🎉 **Daily Bonus Claimed!**

💰 **+💎{daily_bonus_amount}** added to your balance
💎 **New Balance:** **💎{user.balance:.2f}**

*Come back tomorrow for another gift!* 🎁
                """
                
                keyboard = [[InlineKeyboardButton("💰 View Balance", callback_data="balance_refresh")]]
                reply_markup = InlineKeyboardMarkup(keyboard)
                
                await message.edit_text(
                    bonus_text,
                    parse_mode='Markdown',
                    reply_markup=reply_markup
                )
            else:
                await message.edit_text("❌ Failed to claim daily bonus. Please try again.")
                
        except Exception as e:
            logger.error(f"Error in daily bonus: {e}")
            await update.message.reply_text("❌ Failed to claim daily bonus.")
    
    async def handle_referrals_menu(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle referrals menu with immediate display"""
        try:
            user_id = update.effective_user.id
            
            # Check permissions
            permissions = await self.bot.check_user_permissions(user_id)
            if not permissions['allowed']:
                await update.message.reply_text(permissions['reason'])
                return
            
            # Get user and referral stats
            user = await self.services['user'].get_user(user_id)
            if not user:
                await update.message.reply_text("❌ User not found. Please use /start first.")
                return
            
            # Get referral statistics
            try:
                referral_stats = await self.services['referral'].get_user_referral_stats(user_id)
                referral_count = referral_stats.get('successful_referrals', 0)
                total_referrals = referral_stats.get('total_referrals', 0)
            except:
                referral_count = user.successful_referrals
                total_referrals = 0
            
            # Calculate earnings and success rate
            referral_earnings = referral_count * Config.REFERRAL_REWARD
            success_rate = (referral_count / total_referrals * 100) if total_referrals > 0 else 0
            
            referral_text = f"""
👥 **REFERRAL SYSTEM**

• **Successful Referrals:** **{referral_count}** (*💎{referral_earnings:.2f} earned*)
• **Reward per Friend:** **💎{Config.REFERRAL_REWARD}**
• **Success Rate:** *{success_rate:.1f}%*

*Share your link and earn 💎{Config.REFERRAL_REWARD} per friend!*
            """
            
            # Create share URL
            share_url = f"https://t.me/share/url?text=https://t.me/{Config.BOT_USERNAME}?start={user.referral_code}%0AI've%20Got%20Up%20To%20💎100!%20Click%20URL%20To%20Join%20&%20Make%20Money%20Now!"
            
            keyboard = [
                [InlineKeyboardButton("📋 Copy Referral Link", callback_data="copy_referral_link")],
                [InlineKeyboardButton("📤 Share on Telegram", url=share_url)],
                [InlineKeyboardButton("🔄 Refresh Stats", callback_data="balance_refresh")]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)
            
            await update.message.reply_text(
                referral_text,
                parse_mode='Markdown',
                reply_markup=reply_markup
            )
            
        except Exception as e:
            logger.error(f"Error in referrals menu: {e}")
            await update.message.reply_text("❌ Failed to get referral information.")
    
    async def handle_tasks_menu(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle tasks menu with admin-configurable tasks system"""
        try:
            user_id = update.effective_user.id
            
            # Check permissions
            permissions = await self.bot.check_user_permissions(user_id)
            if not permissions['allowed']:
                await update.message.reply_text(permissions['reason'])
                return
            
            # Get available tasks from admin panel
            try:
                available_tasks = await self.services['admin_panel'].get_available_tasks_for_user(user_id)
            except Exception as e:
                logger.error(f"Error getting tasks: {e}")
                available_tasks = []
            
            if not available_tasks:
                no_tasks_text = """
📋 **TASKS CENTER**

🚧 **No tasks available right now**

*Check back later for new earning opportunities!*
                """
                
                keyboard = [[InlineKeyboardButton("🔄 Refresh", callback_data="balance_refresh")]]
                reply_markup = InlineKeyboardMarkup(keyboard)
                
                await update.message.reply_text(
                    no_tasks_text,
                    parse_mode='Markdown',
                    reply_markup=reply_markup
                )
                return
            
            # Calculate total available rewards
            total_available = sum(task['reward'] for task in available_tasks if not task['completed'])
            
            tasks_text = f"""
📋 **TASKS CENTER**

**Available Rewards:** 💎{total_available:.2f}

"""
            
            # Add each task to the display with simple formatting
            for task in available_tasks:
                status_icon = "✅" if task["completed"] else "⏳"
                status_text = "DONE" if task["completed"] else "AVAILABLE"
                
                tasks_text += f"{status_icon} **{task['title']}** - 💎{task['reward']} ({status_text})\n"
            
            tasks_text += "\n*Complete tasks to earn rewards!*"
            
            # Create task buttons for incomplete tasks
            keyboard = []
            for task in available_tasks:
                if not task["completed"]:
                    button_text = f"🎯 {task['title']} (+💎{task['reward']})"
                    keyboard.append([InlineKeyboardButton(button_text, callback_data=f"task_{task['task_id']}")])
            
            # Add refresh button
            keyboard.append([InlineKeyboardButton("🔄 Refresh Tasks", callback_data="balance_refresh")])
            
            reply_markup = InlineKeyboardMarkup(keyboard)
            
            await update.message.reply_text(
                tasks_text,
                parse_mode='Markdown',
                reply_markup=reply_markup
            )
            
        except Exception as e:
            logger.error(f"Error in tasks menu: {e}")
            await update.message.reply_text("❌ Failed to load tasks.")
