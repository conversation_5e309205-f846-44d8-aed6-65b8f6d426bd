"""
Task model for the task management system
"""

from datetime import datetime, timezone
from enum import Enum
from typing import Optional, Dict, Any
from dataclasses import dataclass, asdict


class TaskType(Enum):
    """Task type enumeration"""
    JOIN_CHANNEL = "join_channel"
    SUBMIT_IMAGE = "submit_image"


class TaskStatus(Enum):
    """Task status enumeration"""
    ACTIVE = "active"
    INACTIVE = "inactive"
    DELETED = "deleted"


@dataclass
class Task:
    """Task model for admin-created tasks"""
    
    task_id: str
    task_name: str  # Display name for users
    task_type: TaskType
    reward_amount: float
    is_active: bool = True
    created_date: datetime = None
    created_by: int = None  # Admin user ID
    
    # Task-specific configuration
    channel_id: Optional[str] = None  # For join channel tasks
    join_link: Optional[str] = None   # For join channel tasks
    reference_image: Optional[str] = None  # For image submission tasks
    caption: Optional[str] = None     # Task instructions
    verification_mode: str = "manual"  # manual or auto
    
    # Statistics
    total_completions: int = 0
    total_rewards_distributed: float = 0.0
    
    def __post_init__(self):
        if self.created_date is None:
            self.created_date = datetime.now(timezone.utc)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert task to dictionary for database storage"""
        data = asdict(self)
        data['task_type'] = self.task_type.value
        data['created_date'] = self.created_date.isoformat()
        return data
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Task':
        """Create task from dictionary"""
        if 'task_type' in data:
            data['task_type'] = TaskType(data['task_type'])
        if 'created_date' in data and isinstance(data['created_date'], str):
            data['created_date'] = datetime.fromisoformat(data['created_date'])
        return cls(**data)
    
    def get_formatted_reward(self, currency_symbol: str = "💎") -> str:
        """Get formatted reward amount"""
        return f"{currency_symbol}{self.reward_amount:.2f}"
    
    def is_join_channel_task(self) -> bool:
        """Check if this is a join channel task"""
        return self.task_type == TaskType.JOIN_CHANNEL
    
    def is_image_submission_task(self) -> bool:
        """Check if this is an image submission task"""
        return self.task_type == TaskType.SUBMIT_IMAGE
    
    def get_task_description(self) -> str:
        """Get user-friendly task description"""
        if self.is_join_channel_task():
            return f"Join our channel and earn {self.get_formatted_reward()}"
        elif self.is_image_submission_task():
            return f"Submit required image and earn {self.get_formatted_reward()}"
        return f"Complete task and earn {self.get_formatted_reward()}"
    
    def validate_configuration(self) -> tuple[bool, str]:
        """Validate task configuration"""
        if not self.task_name or not self.task_name.strip():
            return False, "Task name is required"
        
        if self.reward_amount <= 0:
            return False, "Reward amount must be positive"
        
        if self.reward_amount > 1000:
            return False, "Reward amount cannot exceed 💎1000"
        
        if self.is_join_channel_task():
            if not self.channel_id:
                return False, "Channel ID is required for join channel tasks"
            if not self.join_link:
                return False, "Join link is required for join channel tasks"
        
        elif self.is_image_submission_task():
            if not self.caption:
                return False, "Task instructions are required for image submission tasks"
        
        return True, "Task configuration is valid"


class UserTaskStatus(Enum):
    """User task completion status"""
    NOT_STARTED = "not_started"
    IN_PROGRESS = "in_progress"
    PENDING_REVIEW = "pending_review"
    COMPLETED = "completed"
    REJECTED = "rejected"


@dataclass
class UserTask:
    """User task completion tracking"""
    
    user_id: int
    task_id: str
    status: UserTaskStatus = UserTaskStatus.NOT_STARTED
    started_date: Optional[datetime] = None
    completed_date: Optional[datetime] = None
    submission_date: Optional[datetime] = None
    review_date: Optional[datetime] = None
    reviewed_by: Optional[int] = None  # Admin user ID
    rejection_reason: Optional[str] = None
    
    def __post_init__(self):
        if self.started_date is None and self.status != UserTaskStatus.NOT_STARTED:
            self.started_date = datetime.now(timezone.utc)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for database storage"""
        data = asdict(self)
        data['status'] = self.status.value
        
        # Convert datetime objects to ISO strings
        for field in ['started_date', 'completed_date', 'submission_date', 'review_date']:
            if data[field]:
                data[field] = data[field].isoformat()
        
        return data
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'UserTask':
        """Create from dictionary"""
        if 'status' in data:
            data['status'] = UserTaskStatus(data['status'])
        
        # Convert ISO strings back to datetime objects
        for field in ['started_date', 'completed_date', 'submission_date', 'review_date']:
            if data.get(field) and isinstance(data[field], str):
                data[field] = datetime.fromisoformat(data[field])
        
        return cls(**data)
    
    def mark_started(self):
        """Mark task as started"""
        self.status = UserTaskStatus.IN_PROGRESS
        if not self.started_date:
            self.started_date = datetime.now(timezone.utc)
    
    def mark_submitted(self):
        """Mark task as submitted for review"""
        self.status = UserTaskStatus.PENDING_REVIEW
        self.submission_date = datetime.now(timezone.utc)
    
    def mark_completed(self, reviewed_by: int = None):
        """Mark task as completed"""
        self.status = UserTaskStatus.COMPLETED
        self.completed_date = datetime.now(timezone.utc)
        self.review_date = datetime.now(timezone.utc)
        if reviewed_by:
            self.reviewed_by = reviewed_by
    
    def mark_rejected(self, reason: str, reviewed_by: int):
        """Mark task as rejected"""
        self.status = UserTaskStatus.REJECTED
        self.rejection_reason = reason
        self.review_date = datetime.now(timezone.utc)
        self.reviewed_by = reviewed_by
    
    def can_retry(self) -> bool:
        """Check if user can retry this task"""
        return self.status in [UserTaskStatus.NOT_STARTED, UserTaskStatus.REJECTED]
    
    def is_pending_review(self) -> bool:
        """Check if task is pending admin review"""
        return self.status == UserTaskStatus.PENDING_REVIEW
    
    def is_completed(self) -> bool:
        """Check if task is completed"""
        return self.status == UserTaskStatus.COMPLETED


class SubmissionStatus(Enum):
    """Task submission review status"""
    PENDING = "pending"
    APPROVED = "approved"
    REJECTED = "rejected"


@dataclass
class TaskSubmission:
    """Task submission for admin review"""
    
    submission_id: str
    user_id: int
    task_id: str
    submitted_image: Optional[str] = None  # File path or URL
    submission_text: Optional[str] = None  # Additional text from user
    submission_date: datetime = None
    review_status: SubmissionStatus = SubmissionStatus.PENDING
    reviewed_by: Optional[int] = None
    review_date: Optional[datetime] = None
    review_notes: Optional[str] = None
    
    def __post_init__(self):
        if self.submission_date is None:
            self.submission_date = datetime.now(timezone.utc)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for database storage"""
        data = asdict(self)
        data['review_status'] = self.review_status.value
        
        # Convert datetime objects to ISO strings
        for field in ['submission_date', 'review_date']:
            if data[field]:
                data[field] = data[field].isoformat()
        
        return data
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'TaskSubmission':
        """Create from dictionary"""
        if 'review_status' in data:
            data['review_status'] = SubmissionStatus(data['review_status'])
        
        # Convert ISO strings back to datetime objects
        for field in ['submission_date', 'review_date']:
            if data.get(field) and isinstance(data[field], str):
                data[field] = datetime.fromisoformat(data[field])
        
        return cls(**data)
    
    def approve(self, reviewed_by: int, notes: str = None):
        """Approve the submission"""
        self.review_status = SubmissionStatus.APPROVED
        self.reviewed_by = reviewed_by
        self.review_date = datetime.now(timezone.utc)
        self.review_notes = notes
    
    def reject(self, reviewed_by: int, notes: str):
        """Reject the submission"""
        self.review_status = SubmissionStatus.REJECTED
        self.reviewed_by = reviewed_by
        self.review_date = datetime.now(timezone.utc)
        self.review_notes = notes
    
    def is_pending(self) -> bool:
        """Check if submission is pending review"""
        return self.review_status == SubmissionStatus.PENDING
    
    def is_approved(self) -> bool:
        """Check if submission is approved"""
        return self.review_status == SubmissionStatus.APPROVED
    
    def is_rejected(self) -> bool:
        """Check if submission is rejected"""
        return self.review_status == SubmissionStatus.REJECTED
