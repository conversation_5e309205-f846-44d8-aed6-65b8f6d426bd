"""
Helper utilities for the Telegram Referral Bot
"""

import re
import qrcode
import io
import base64
from datetime import datetime, timed<PERSON>ta
from typing import Optional, Dict, Any, List
from PIL import Image, ImageDraw, ImageFont
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from config import Config

class MessageFormatter:
    """Format messages for better user experience"""
    
    @staticmethod
    def format_balance(balance: float, total_earned: float = None) -> str:
        """Format balance message"""
        message = f"💰 **Your Balance**\n\n"
        message += f"Current Balance: **💎{balance:.2f}**\n"

        if total_earned is not None:
            message += f"Total Earned: **💎{total_earned:.2f}**\n"

        if balance >= Config.MINIMUM_WITHDRAWAL:
            message += f"\n✅ You can withdraw! (Min: 💎{Config.MINIMUM_WITHDRAWAL})"
        else:
            needed = Config.MINIMUM_WITHDRAWAL - balance
            message += f"\n📈 Earn 💎{needed:.2f} more to withdraw!"

        return message
    
    @staticmethod
    def format_referral_info(user_referral_code: str, referral_count: int, total_earned: float) -> str:
        """Format referral information"""
        referral_link = f"https://t.me/{Config.BOT_USERNAME}?start={user_referral_code}"
        
        message = f"👥 **Your Referral Program**\n\n"
        message += f"🔗 Your Referral Link:\n`{referral_link}`\n\n"
        message += f"📊 **Statistics:**\n"
        message += f"• Successful Referrals: **{referral_count}**\n"
        message += f"• Earned from Referrals: **₹{total_earned:.2f}**\n"
        message += f"• Reward per Referral: **₹{Config.REFERRAL_REWARD}**\n\n"
        message += f"💡 **How it works:**\n"
        message += f"1. Share your link with friends\n"
        message += f"2. They join using your link\n"
        message += f"3. You earn ₹{Config.REFERRAL_REWARD} per referral!\n"
        
        return message
    
    @staticmethod
    def format_transaction_history(transactions: List[Dict]) -> str:
        """Format transaction history"""
        if not transactions:
            return "📋 **Transaction History**\n\nNo transactions found."
        
        message = "📋 **Transaction History**\n\n"
        
        for transaction in transactions[:10]:  # Show last 10 transactions
            date = transaction['created_at'].strftime('%d/%m/%Y %H:%M')
            amount = transaction['amount']
            tx_type = transaction['transaction_type']
            status = transaction['status']
            
            emoji = "✅" if status == "completed" else "⏳" if status == "pending" else "❌"
            sign = "+" if amount > 0 else ""

            message += f"{emoji} {sign}💎{amount:.2f} - {tx_type.replace('_', ' ').title()}\n"
            message += f"   📅 {date}\n\n"
        
        if len(transactions) > 10:
            message += f"... and {len(transactions) - 10} more transactions"
        
        return message
    
    @staticmethod
    def format_withdrawal_request(withdrawal: Dict) -> str:
        """Format withdrawal request details"""
        message = f"💸 **Withdrawal Request**\n\n"
        message += f"Amount: **💎{withdrawal['amount']:.2f}**\n"
        message += f"Type: **{withdrawal['withdrawal_type'].replace('_', ' ').title()}**\n"
        message += f"Status: **{withdrawal['status'].replace('_', ' ').title()}**\n"
        message += f"Date: **{withdrawal['created_at'].strftime('%d/%m/%Y %H:%M')}**\n"

        if withdrawal.get('product_name'):
            message += f"Product: **{withdrawal['product_name']}**\n"

        if withdrawal.get('admin_notes'):
            message += f"\n📝 Admin Notes: {withdrawal['admin_notes']}"

        return message
    
    @staticmethod
    def format_user_stats(user: Dict, referrals: int, transactions: int) -> str:
        """Format user statistics"""
        join_date = user['created_at'].strftime('%d/%m/%Y')
        last_active = user['last_activity'].strftime('%d/%m/%Y %H:%M')
        
        message = f"📊 **Your Statistics**\n\n"
        message += f"👤 **Profile:**\n"
        message += f"• Name: {user.get('first_name', 'N/A')}\n"
        message += f"• Username: @{user.get('username', 'N/A')}\n"
        message += f"• User ID: `{user['user_id']}`\n"
        message += f"• Joined: {join_date}\n"
        message += f"• Last Active: {last_active}\n\n"
        
        message += f"💰 **Earnings:**\n"
        message += f"• Current Balance: ₹{user['balance']:.2f}\n"
        message += f"• Total Earned: ₹{user['total_earned']:.2f}\n"
        message += f"• Total Withdrawals: ₹{user.get('total_withdrawals', 0):.2f}\n\n"
        
        message += f"👥 **Referrals:**\n"
        message += f"• Successful Referrals: {referrals}\n"
        message += f"• Referral Earnings: ₹{referrals * Config.REFERRAL_REWARD:.2f}\n\n"
        
        message += f"📈 **Activity:**\n"
        message += f"• Total Transactions: {transactions}\n"
        message += f"• Account Status: {'✅ Active' if user['is_active'] else '❌ Inactive'}\n"
        
        return message

class QRCodeGenerator:
    """Generate QR codes for referral links"""
    
    @staticmethod
    def generate_referral_qr(referral_link: str) -> io.BytesIO:
        """Generate QR code for referral link"""
        qr = qrcode.QRCode(
            version=1,
            error_correction=qrcode.constants.ERROR_CORRECT_L,
            box_size=10,
            border=4,
        )
        qr.add_data(referral_link)
        qr.make(fit=True)
        
        # Create QR code image
        img = qr.make_image(fill_color="black", back_color="white")
        
        # Convert to bytes
        bio = io.BytesIO()
        img.save(bio, format='PNG')
        bio.seek(0)
        
        return bio

class ChartGenerator:
    """Generate charts for analytics"""
    
    @staticmethod
    def generate_earnings_chart(data: List[Dict], days: int = 30) -> io.BytesIO:
        """Generate earnings chart"""
        plt.figure(figsize=(10, 6))
        
        # Prepare data
        dates = [item['date'] for item in data]
        earnings = [item['amount'] for item in data]
        
        # Create chart
        plt.plot(dates, earnings, marker='o', linewidth=2, markersize=6)
        plt.title(f'Earnings Over Last {days} Days', fontsize=16, fontweight='bold')
        plt.xlabel('Date', fontsize=12)
        plt.ylabel('Earnings (₹)', fontsize=12)
        plt.grid(True, alpha=0.3)
        
        # Format dates
        plt.gca().xaxis.set_major_formatter(mdates.DateFormatter('%m/%d'))
        plt.gca().xaxis.set_major_locator(mdates.DayLocator(interval=5))
        plt.xticks(rotation=45)
        
        plt.tight_layout()
        
        # Save to bytes
        bio = io.BytesIO()
        plt.savefig(bio, format='PNG', dpi=150, bbox_inches='tight')
        bio.seek(0)
        plt.close()
        
        return bio
    
    @staticmethod
    def generate_referral_chart(data: List[Dict]) -> io.BytesIO:
        """Generate referral statistics chart"""
        plt.figure(figsize=(10, 6))
        
        # Prepare data
        dates = [item['date'] for item in data]
        referrals = [item['count'] for item in data]
        
        # Create bar chart
        plt.bar(dates, referrals, color='skyblue', alpha=0.7)
        plt.title('Daily Referrals', fontsize=16, fontweight='bold')
        plt.xlabel('Date', fontsize=12)
        plt.ylabel('Number of Referrals', fontsize=12)
        plt.grid(True, alpha=0.3, axis='y')
        
        # Format dates
        plt.gca().xaxis.set_major_formatter(mdates.DateFormatter('%m/%d'))
        plt.xticks(rotation=45)
        
        plt.tight_layout()
        
        # Save to bytes
        bio = io.BytesIO()
        plt.savefig(bio, format='PNG', dpi=150, bbox_inches='tight')
        bio.seek(0)
        plt.close()
        
        return bio

class TextUtils:
    """Text processing utilities"""
    
    @staticmethod
    def extract_referral_code(text: str) -> Optional[str]:
        """Extract referral code from start command"""
        if not text or not text.startswith('/start'):
            return None
        
        parts = text.split()
        if len(parts) > 1:
            code = parts[1].strip()
            if len(code) == 8 and code.isalnum():
                return code.upper()
        
        return None
    
    @staticmethod
    def format_duration(seconds: int) -> str:
        """Format duration in human readable format"""
        if seconds < 60:
            return f"{seconds} seconds"
        elif seconds < 3600:
            minutes = seconds // 60
            return f"{minutes} minutes"
        elif seconds < 86400:
            hours = seconds // 3600
            return f"{hours} hours"
        else:
            days = seconds // 86400
            return f"{days} days"
    
    @staticmethod
    def truncate_text(text: str, max_length: int = 100) -> str:
        """Truncate text with ellipsis"""
        if len(text) <= max_length:
            return text
        
        return text[:max_length-3] + "..."
    
    @staticmethod
    def escape_markdown(text: str) -> str:
        """Escape markdown special characters"""
        special_chars = ['_', '*', '[', ']', '(', ')', '~', '`', '>', '#', '+', '-', '=', '|', '{', '}', '.', '!']
        
        for char in special_chars:
            text = text.replace(char, f'\\{char}')
        
        return text
    
    @staticmethod
    def validate_username(username: str) -> bool:
        """Validate Telegram username format"""
        if not username:
            return False
        
        # Remove @ if present
        username = username.lstrip('@')
        
        # Check format: 5-32 characters, alphanumeric + underscore
        pattern = r'^[a-zA-Z0-9_]{5,32}$'
        return bool(re.match(pattern, username))

class DateUtils:
    """Date and time utilities"""
    
    @staticmethod
    def get_today_start() -> datetime:
        """Get start of today"""
        now = datetime.now()
        return datetime(now.year, now.month, now.day)
    
    @staticmethod
    def get_week_start() -> datetime:
        """Get start of current week"""
        now = datetime.now()
        days_since_monday = now.weekday()
        return now - timedelta(days=days_since_monday)
    
    @staticmethod
    def get_month_start() -> datetime:
        """Get start of current month"""
        now = datetime.now()
        return datetime(now.year, now.month, 1)
    
    @staticmethod
    def time_until_next_day() -> timedelta:
        """Get time until next day"""
        now = datetime.now()
        tomorrow = datetime(now.year, now.month, now.day) + timedelta(days=1)
        return tomorrow - now
    
    @staticmethod
    def format_relative_time(dt: datetime) -> str:
        """Format datetime as relative time"""
        now = datetime.now()
        diff = now - dt
        
        if diff.days > 0:
            return f"{diff.days} days ago"
        elif diff.seconds > 3600:
            hours = diff.seconds // 3600
            return f"{hours} hours ago"
        elif diff.seconds > 60:
            minutes = diff.seconds // 60
            return f"{minutes} minutes ago"
        else:
            return "Just now"
