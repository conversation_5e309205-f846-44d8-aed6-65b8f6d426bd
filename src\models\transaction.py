"""
Transaction model for the Telegram Referral Bot
"""

from datetime import datetime, timezone
from typing import Optional, Dict, Any
from dataclasses import dataclass, asdict
from enum import Enum
import uuid

class TransactionType(Enum):
    """Transaction types"""
    REFERRAL_BONUS = "referral_bonus"
    DAILY_BONUS = "daily_bonus"
    WITHDRAWAL = "withdrawal"
    ADMIN_CREDIT = "admin_credit"
    ADMIN_DEBIT = "admin_debit"
    PRODUCT_PURCHASE = "product_purchase"

class TransactionStatus(Enum):
    """Transaction status"""
    PENDING = "pending"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"

@dataclass
class Transaction:
    """Transaction model representing financial transactions"""
    
    user_id: int
    amount: float
    transaction_type: TransactionType
    status: TransactionStatus = TransactionStatus.PENDING
    
    # Optional fields
    transaction_id: Optional[str] = None
    description: Optional[str] = None
    reference_id: Optional[str] = None  # For referrals, withdrawals, etc.
    metadata: Optional[Dict[str, Any]] = None
    
    # Timestamps
    created_at: datetime = None
    updated_at: datetime = None
    completed_at: Optional[datetime] = None
    
    # Admin fields
    processed_by: Optional[int] = None  # Admin user ID
    admin_notes: Optional[str] = None
    
    def __post_init__(self):
        """Initialize default values after object creation"""
        if self.transaction_id is None:
            self.transaction_id = str(uuid.uuid4())
        
        if self.created_at is None:
            self.created_at = datetime.now(timezone.utc)
        
        if self.updated_at is None:
            self.updated_at = datetime.now(timezone.utc)
        
        if self.metadata is None:
            self.metadata = {}
    
    def complete_transaction(self, admin_id: Optional[int] = None, notes: Optional[str] = None):
        """Mark transaction as completed"""
        self.status = TransactionStatus.COMPLETED
        self.completed_at = datetime.now(timezone.utc)
        self.updated_at = datetime.now(timezone.utc)
        
        if admin_id:
            self.processed_by = admin_id
        
        if notes:
            self.admin_notes = notes
    
    def fail_transaction(self, reason: str = "", admin_id: Optional[int] = None):
        """Mark transaction as failed"""
        self.status = TransactionStatus.FAILED
        self.updated_at = datetime.now(timezone.utc)
        
        if reason:
            self.admin_notes = reason
        
        if admin_id:
            self.processed_by = admin_id
    
    def cancel_transaction(self, reason: str = "", admin_id: Optional[int] = None):
        """Cancel transaction"""
        self.status = TransactionStatus.CANCELLED
        self.updated_at = datetime.now(timezone.utc)
        
        if reason:
            self.admin_notes = reason
        
        if admin_id:
            self.processed_by = admin_id
    
    def add_metadata(self, key: str, value: Any):
        """Add metadata to transaction"""
        self.metadata[key] = value
        self.updated_at = datetime.now(timezone.utc)
    
    def get_formatted_amount(self, currency_symbol: str = "💎") -> str:
        """Get formatted amount with currency symbol"""
        return f"{currency_symbol}{self.amount:.2f}"
    
    def get_type_display(self) -> str:
        """Get human-readable transaction type"""
        type_map = {
            TransactionType.REFERRAL_BONUS: "Referral Bonus",
            TransactionType.DAILY_BONUS: "Daily Bonus",
            TransactionType.WITHDRAWAL: "Withdrawal",
            TransactionType.ADMIN_CREDIT: "Admin Credit",
            TransactionType.ADMIN_DEBIT: "Admin Debit",
            TransactionType.PRODUCT_PURCHASE: "Product Purchase"
        }
        return type_map.get(self.transaction_type, str(self.transaction_type.value))
    
    def get_status_display(self) -> str:
        """Get human-readable status"""
        status_map = {
            TransactionStatus.PENDING: "⏳ Pending",
            TransactionStatus.COMPLETED: "✅ Completed",
            TransactionStatus.FAILED: "❌ Failed",
            TransactionStatus.CANCELLED: "🚫 Cancelled"
        }
        return status_map.get(self.status, str(self.status.value))
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert transaction object to dictionary"""
        data = asdict(self)
        
        # Convert enum values to strings
        data['transaction_type'] = self.transaction_type.value
        data['status'] = self.status.value
        
        # Convert datetime objects to ISO format
        datetime_fields = ['created_at', 'updated_at', 'completed_at']
        for field in datetime_fields:
            if field in data and data[field]:
                data[field] = data[field].isoformat()
        
        return data
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Transaction':
        """Create transaction object from dictionary"""
        # Create a copy of data to avoid modifying the original
        transaction_data = data.copy()

        # Remove MongoDB's _id field if present
        transaction_data.pop('_id', None)

        # Convert string values back to enums
        if 'transaction_type' in transaction_data:
            transaction_data['transaction_type'] = TransactionType(transaction_data['transaction_type'])

        if 'status' in transaction_data:
            transaction_data['status'] = TransactionStatus(transaction_data['status'])

        # Convert ISO format strings back to datetime objects
        datetime_fields = ['created_at', 'updated_at', 'completed_at']
        for field in datetime_fields:
            if field in transaction_data and transaction_data[field]:
                if isinstance(transaction_data[field], str):
                    transaction_data[field] = datetime.fromisoformat(transaction_data[field].replace('Z', '+00:00'))

        return cls(**transaction_data)
    
    def __str__(self) -> str:
        """String representation of transaction"""
        return f"Transaction(id={self.transaction_id}, user={self.user_id}, amount={self.amount}, type={self.transaction_type.value})"
