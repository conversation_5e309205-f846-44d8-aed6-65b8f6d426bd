#!/bin/bash

# Production startup script for Telegram Referral Bot

echo "🤖 Starting Telegram Referral Bot..."

# Check if virtual environment exists
if [ ! -d "venv" ]; then
    echo "Creating virtual environment..."
    python3 -m venv venv
fi

# Activate virtual environment
source venv/bin/activate

# Install/update dependencies
echo "Installing dependencies..."
pip install -r requirements.txt

# Check if .env file exists
if [ ! -f ".env" ]; then
    echo "❌ Error: .env file not found!"
    echo "Please copy .env.example to .env and configure it."
    exit 1
fi

# Create logs directory
mkdir -p logs

# Start the bot
echo "🚀 Starting bot..."
python bot.py
