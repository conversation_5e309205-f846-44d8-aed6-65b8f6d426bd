"""
Admin command handlers for the Telegram bot
"""

from telegram import Update, InlineKeyboardMarkup, InlineKeyboardButton
from telegram.ext import ContextTypes

from config import Config
from src.utils.logger import setup_logger, log_admin_action

logger = setup_logger(__name__)


class AdminHandlers:
    """Handles admin-related commands and interactions"""
    
    def __init__(self, bot_instance):
        self.bot = bot_instance
        self.services = bot_instance.services
    
    async def admin_panel_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Main admin panel entry point"""
        try:
            user_id = update.effective_user.id

            # Check admin access
            if not await self.services['admin_panel'].validate_admin_access(user_id, Config.ADMIN_USER_IDS):
                await update.message.reply_text("❌ Access denied. Admin privileges required.")
                return

            # Show main admin menu
            await self.show_admin_main_menu(update, context)

        except Exception as e:
            logger.error(f"Error in admin panel command: {e}")
            await update.message.reply_text("❌ Failed to access admin panel.")
    
    async def show_admin_main_menu(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Show main admin panel menu"""
        try:
            admin_text = """
🔧 **ADMIN CONTROL PANEL** 🔧

*Welcome to the bot administration center.*

*Select a section below to get started:*
            """

            keyboard = [
                [InlineKeyboardButton("👥 User Management", callback_data="admin_users"),
                 InlineKeyboardButton("⚙️ Bot Settings", callback_data="admin_settings_menu")],
                [InlineKeyboardButton("📋 Task Management", callback_data="admin_tasks"),
                 InlineKeyboardButton("📢 Broadcasting", callback_data="admin_broadcast")],
                [InlineKeyboardButton("📊 Analytics", callback_data="admin_analytics"),
                 InlineKeyboardButton("🔒 System Health", callback_data="admin_health")],
                [InlineKeyboardButton("📋 Admin Logs", callback_data="admin_logs")]
            ]

            reply_markup = InlineKeyboardMarkup(keyboard)

            await update.message.reply_text(
                admin_text,
                parse_mode='Markdown',
                reply_markup=reply_markup
            )

        except Exception as e:
            logger.error(f"Error showing admin main menu: {e}")
            await update.message.reply_text("❌ Failed to load admin menu.")
    
    async def show_admin_main_menu_callback(self, query, context):
        """Show main admin panel menu for callback queries"""
        try:
            admin_text = """
🔧 **ADMIN CONTROL PANEL** 🔧

*Welcome to the bot administration center.*

*Select a section below to get started:*
            """

            keyboard = [
                [InlineKeyboardButton("👥 User Management", callback_data="admin_users"),
                 InlineKeyboardButton("⚙️ Bot Settings", callback_data="admin_settings_menu")],
                [InlineKeyboardButton("📋 Task Management", callback_data="admin_tasks"),
                 InlineKeyboardButton("📢 Broadcasting", callback_data="admin_broadcast")],
                [InlineKeyboardButton("📊 Analytics", callback_data="admin_analytics"),
                 InlineKeyboardButton("🔒 System Health", callback_data="admin_health")],
                [InlineKeyboardButton("📋 Admin Logs", callback_data="admin_logs")]
            ]

            reply_markup = InlineKeyboardMarkup(keyboard)

            await query.edit_message_text(
                admin_text,
                parse_mode='Markdown',
                reply_markup=reply_markup
            )

        except Exception as e:
            logger.error(f"Error showing admin main menu callback: {e}")
            await query.answer("❌ Failed to load admin menu.")
    
    async def show_user_management_menu(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Show user management menu"""
        try:
            # Get basic user statistics
            stats = await self.services['admin_panel'].get_user_statistics()
            
            menu_text = f"""
👥 **USER MANAGEMENT** 👥

• **Total Users:** {stats.get('total_users', 0)}
• **Active Users (7 days):** {stats.get('active_7_days', 0)}
• **New Users (24h):** {stats.get('new_24h', 0)}

*Select an option below:*
            """

            keyboard = [
                [InlineKeyboardButton("📋 View Users List", callback_data="admin_users_list"),
                 InlineKeyboardButton("📊 User Reports", callback_data="admin_user_reports")],
                [InlineKeyboardButton("🔍 Search User", callback_data="admin_search_user"),
                 InlineKeyboardButton("💰 Manage Balances", callback_data="admin_manage_balances")],
                [InlineKeyboardButton("🚫 Ban/Unban Users", callback_data="admin_ban_users"),
                 InlineKeyboardButton("📢 Message User", callback_data="admin_message_user")],
                [InlineKeyboardButton("🔙 Back to Main Menu", callback_data="admin_main")]
            ]

            reply_markup = InlineKeyboardMarkup(keyboard)

            await update.callback_query.edit_message_text(
                menu_text,
                parse_mode='Markdown',
                reply_markup=reply_markup
            )

        except Exception as e:
            logger.error(f"Error showing user management menu: {e}")
            await update.callback_query.answer("❌ Failed to load user management menu.")
    
    async def show_user_management_menu_callback(self, query, context):
        """Show user management menu for callback queries"""
        try:
            # Get basic user statistics
            stats = await self.services['admin_panel'].get_user_statistics()
            
            menu_text = f"""
👥 **USER MANAGEMENT** 👥

• **Total Users:** {stats.get('total_users', 0)}
• **Active Users (7 days):** {stats.get('active_7_days', 0)}
• **New Users (24h):** {stats.get('new_24h', 0)}

*Select an option below:*
            """

            keyboard = [
                [InlineKeyboardButton("📋 View Users List", callback_data="admin_users_list"),
                 InlineKeyboardButton("📊 User Reports", callback_data="admin_user_reports")],
                [InlineKeyboardButton("🔍 Search User", callback_data="admin_search_user"),
                 InlineKeyboardButton("💰 Manage Balances", callback_data="admin_manage_balances")],
                [InlineKeyboardButton("🚫 Ban/Unban Users", callback_data="admin_ban_users"),
                 InlineKeyboardButton("📢 Message User", callback_data="admin_message_user")],
                [InlineKeyboardButton("🔙 Back to Main Menu", callback_data="admin_main")]
            ]

            reply_markup = InlineKeyboardMarkup(keyboard)

            await query.edit_message_text(
                menu_text,
                parse_mode='Markdown',
                reply_markup=reply_markup
            )

        except Exception as e:
            logger.error(f"Error showing user management menu callback: {e}")
            await query.answer("❌ Failed to load user management menu.")
    
    async def show_bot_settings_menu(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Show bot settings management menu"""
        try:
            # Get current settings
            settings = await self.services['admin_settings'].get_all_settings()
            
            settings_text = f"""
⚙️ **BOT SETTINGS** ⚙️

**Current Configuration:**

• **Referral Reward:** 💎{settings.referral_reward}
• **Daily Bonus:** 💎{settings.daily_bonus_amount}
• **Min Withdrawal:** 💎{settings.minimum_withdrawal}

• **Welcome Bonus:** 💎{settings.welcome_bonus}

*Select a setting to modify:*
            """

            keyboard = [
                [InlineKeyboardButton("💰 Referral Reward", callback_data="admin_set_referral"),
                 InlineKeyboardButton("🎁 Daily Bonus", callback_data="admin_set_daily")],
                [InlineKeyboardButton("💸 Min Withdrawal", callback_data="admin_set_withdrawal"),
                 InlineKeyboardButton("⏰ Withdrawal Cooldown", callback_data="admin_set_cooldown")],
                [InlineKeyboardButton("🎊 Welcome Bonus", callback_data="admin_set_welcome"),
                 InlineKeyboardButton("🔢 Daily Limits", callback_data="admin_set_limits")],
                [InlineKeyboardButton("🔄 Reset to Defaults", callback_data="admin_reset_settings"),
                 InlineKeyboardButton("💾 Export Settings", callback_data="admin_export_settings")],
                [InlineKeyboardButton("🔙 Back to Main Menu", callback_data="admin_main")]
            ]

            reply_markup = InlineKeyboardMarkup(keyboard)

            await update.callback_query.edit_message_text(
                settings_text,
                parse_mode='Markdown',
                reply_markup=reply_markup
            )

        except Exception as e:
            logger.error(f"Error showing bot settings menu: {e}")
            await update.callback_query.answer("❌ Failed to load settings menu.")
    
    async def show_bot_settings_menu_callback(self, query, context):
        """Show bot settings management menu for callback queries"""
        try:
            # Get current settings
            settings = await self.services['admin_settings'].get_all_settings()
            
            settings_text = f"""
⚙️ **BOT SETTINGS** ⚙️

**Current Configuration:**

• **Referral Reward:** 💎{settings.referral_reward}
• **Daily Bonus:** 💎{settings.daily_bonus_amount}
• **Min Withdrawal:** 💎{settings.minimum_withdrawal}

• **Welcome Bonus:** 💎{settings.welcome_bonus}

*Select a setting to modify:*
            """

            keyboard = [
                [InlineKeyboardButton("💰 Referral Reward", callback_data="admin_set_referral"),
                 InlineKeyboardButton("🎁 Daily Bonus", callback_data="admin_set_daily")],
                [InlineKeyboardButton("💸 Min Withdrawal", callback_data="admin_set_withdrawal"),
                 InlineKeyboardButton("⏰ Withdrawal Cooldown", callback_data="admin_set_cooldown")],
                [InlineKeyboardButton("🎊 Welcome Bonus", callback_data="admin_set_welcome"),
                 InlineKeyboardButton("🔢 Daily Limits", callback_data="admin_set_limits")],
                [InlineKeyboardButton("🔄 Reset to Defaults", callback_data="admin_reset_settings"),
                 InlineKeyboardButton("💾 Export Settings", callback_data="admin_export_settings")],
                [InlineKeyboardButton("🔙 Back to Main Menu", callback_data="admin_main")]
            ]

            reply_markup = InlineKeyboardMarkup(keyboard)

            await query.edit_message_text(
                settings_text,
                parse_mode='Markdown',
                reply_markup=reply_markup
            )

        except Exception as e:
            logger.error(f"Error showing bot settings menu callback: {e}")
            await query.answer("❌ Failed to load settings menu.")
