2025-07-01 05:11:47 - src - INFO - main:29 - Starting Telegram Referral Earning Bot...
2025-07-01 05:11:47 - src - INFO - main:30 - Mode: Long Polling (no webhook/domain required)
2025-07-01 05:11:52 - src.database - ERROR - _initialize_collections:100 - Error initializing collections: Index build failed: fd679877-cef7-4fcf-bec7-5d0cda3b7ff1: Collection referral_bot.users ( ee2886e1-84e7-4639-b15f-9501d14afb5f ) :: caused by :: E11000 duplicate key error collection: referral_bot.users index: referral_code_1 dup key: { referral_code: null }, full error: {'ok': 0.0, 'errmsg': 'Index build failed: fd679877-cef7-4fcf-bec7-5d0cda3b7ff1: Collection referral_bot.users ( ee2886e1-84e7-4639-b15f-9501d14afb5f ) :: caused by :: E11000 duplicate key error collection: referral_bot.users index: referral_code_1 dup key: { referral_code: null }', 'code': 11000, 'codeName': 'DuplicateKey', 'keyPattern': {'referral_code': 1}, 'keyValue': {'referral_code': None}, '$clusterTime': {'clusterTime': Timestamp(1751326912, 28), 'signature': {'hash': b'\x89\x80\xb6A2(\x10\xde#\x90i\xdab\xd5\xd1?\xa9\x86\xb8\xb7', 'keyId': 7471222609279975432}}, 'operationTime': Timestamp(1751326912, 28)}
2025-07-01 05:11:52 - src.database - INFO - connect:35 - Successfully connected to MongoDB Atlas
2025-07-01 05:11:52 - src - INFO - main:35 - Database connected successfully
2025-07-01 05:11:52 - src.bot - INFO - __init__:49 - ReferralBot initialized
2025-07-01 05:11:52 - src.bot - INFO - __init__:49 - ReferralBot initialized
2025-07-01 05:11:53 - src.bot - INFO - _add_handlers:101 - All handlers added successfully
2025-07-01 05:11:53 - src.bot - INFO - _add_handlers:101 - All handlers added successfully
2025-07-01 05:11:53 - src.services.product_service - ERROR - initialize_default_products:400 - Failed to initialize default products: Database objects do not implement truth value testing or bool(). Please compare with None instead: database is not None
2025-07-01 05:11:53 - src.bot - ERROR - _initialize_bot_data:115 - Failed to initialize bot data: Database objects do not implement truth value testing or bool(). Please compare with None instead: database is not None
2025-07-01 05:11:53 - src.bot - ERROR - _initialize_bot_data:115 - Failed to initialize bot data: Database objects do not implement truth value testing or bool(). Please compare with None instead: database is not None
2025-07-01 05:11:53 - src.bot - INFO - _start_polling:139 - Starting bot in long polling mode...
2025-07-01 05:11:53 - src.bot - INFO - _start_polling:139 - Starting bot in long polling mode...
2025-07-01 05:11:57 - src.bot - INFO - _start_polling:154 - Bot is running in long polling mode. Press Ctrl+C to stop.
2025-07-01 05:11:57 - src.bot - INFO - _start_polling:154 - Bot is running in long polling mode. Press Ctrl+C to stop.
2025-07-01 05:11:57 - src.bot - INFO - _start_polling:155 - No webhook, domain, or SSL configuration required.
2025-07-01 05:11:57 - src.bot - INFO - _start_polling:155 - No webhook, domain, or SSL configuration required.
2025-07-01 05:11:57 - src.bot - ERROR - start:67 - Failed to start bot: 'Updater' object has no attribute 'idle'
2025-07-01 05:11:57 - src.bot - ERROR - start:67 - Failed to start bot: 'Updater' object has no attribute 'idle'
2025-07-01 05:12:43 - src - INFO - main:29 - Starting Telegram Referral Earning Bot...
2025-07-01 05:12:43 - src - INFO - main:30 - Mode: Long Polling (no webhook/domain required)
2025-07-01 05:12:48 - src.database - ERROR - _initialize_collections:100 - Error initializing collections: Index build failed: e36f9146-93e7-4900-8434-2c3182817196: Collection referral_bot.users ( ee2886e1-84e7-4639-b15f-9501d14afb5f ) :: caused by :: E11000 duplicate key error collection: referral_bot.users index: referral_code_1 dup key: { referral_code: null }, full error: {'ok': 0.0, 'errmsg': 'Index build failed: e36f9146-93e7-4900-8434-2c3182817196: Collection referral_bot.users ( ee2886e1-84e7-4639-b15f-9501d14afb5f ) :: caused by :: E11000 duplicate key error collection: referral_bot.users index: referral_code_1 dup key: { referral_code: null }', 'code': 11000, 'codeName': 'DuplicateKey', 'keyPattern': {'referral_code': 1}, 'keyValue': {'referral_code': None}, '$clusterTime': {'clusterTime': Timestamp(1751326968, 15), 'signature': {'hash': b'\xfcrZ\xc0\x8e\x7f\xedu\xe5\xd6hl\xb2\x19D\x14\xa99\x17\xed', 'keyId': 7471222609279975432}}, 'operationTime': Timestamp(1751326968, 15)}
2025-07-01 05:12:48 - src.database - INFO - connect:35 - Successfully connected to MongoDB Atlas
2025-07-01 05:12:48 - src - INFO - main:35 - Database connected successfully
2025-07-01 05:12:48 - src.bot - INFO - __init__:49 - ReferralBot initialized
2025-07-01 05:12:48 - src.bot - INFO - __init__:49 - ReferralBot initialized
2025-07-01 05:12:49 - src.bot - INFO - _add_handlers:101 - All handlers added successfully
2025-07-01 05:12:49 - src.bot - INFO - _add_handlers:101 - All handlers added successfully
2025-07-01 05:12:49 - src.services.product_service - ERROR - initialize_default_products:400 - Failed to initialize default products: Database objects do not implement truth value testing or bool(). Please compare with None instead: database is not None
2025-07-01 05:12:49 - src.bot - ERROR - _initialize_bot_data:115 - Failed to initialize bot data: Database objects do not implement truth value testing or bool(). Please compare with None instead: database is not None
2025-07-01 05:12:49 - src.bot - ERROR - _initialize_bot_data:115 - Failed to initialize bot data: Database objects do not implement truth value testing or bool(). Please compare with None instead: database is not None
2025-07-01 05:12:49 - src.bot - INFO - _start_polling:139 - Starting bot in long polling mode...
2025-07-01 05:12:49 - src.bot - INFO - _start_polling:139 - Starting bot in long polling mode...
2025-07-01 05:12:50 - src.bot - INFO - _start_polling:154 - Bot is running in long polling mode. Press Ctrl+C to stop.
2025-07-01 05:12:50 - src.bot - INFO - _start_polling:154 - Bot is running in long polling mode. Press Ctrl+C to stop.
2025-07-01 05:12:50 - src.bot - INFO - _start_polling:155 - No webhook, domain, or SSL configuration required.
2025-07-01 05:12:50 - src.bot - INFO - _start_polling:155 - No webhook, domain, or SSL configuration required.
2025-07-01 05:12:50 - src.bot - ERROR - start:67 - Failed to start bot: 'Updater' object has no attribute 'idle'
2025-07-01 05:12:50 - src.bot - ERROR - start:67 - Failed to start bot: 'Updater' object has no attribute 'idle'
2025-07-01 05:14:34 - src - INFO - main:29 - Starting Telegram Referral Earning Bot...
2025-07-01 05:14:34 - src - INFO - main:30 - Mode: Long Polling (no webhook/domain required)
2025-07-01 05:14:42 - src.database - INFO - _initialize_collections:97 - Database collections and indexes initialized
2025-07-01 05:14:42 - src.database - INFO - connect:35 - Successfully connected to MongoDB Atlas
2025-07-01 05:14:42 - src - INFO - main:35 - Database connected successfully
2025-07-01 05:14:42 - src.bot - INFO - __init__:49 - ReferralBot initialized
2025-07-01 05:14:42 - src.bot - INFO - __init__:49 - ReferralBot initialized
2025-07-01 05:14:42 - src.bot - INFO - _add_handlers:101 - All handlers added successfully
2025-07-01 05:14:42 - src.bot - INFO - _add_handlers:101 - All handlers added successfully
2025-07-01 05:14:42 - src.services.product_service - ERROR - initialize_default_products:404 - Failed to initialize default products: Database objects do not implement truth value testing or bool(). Please compare with None instead: database is not None
2025-07-01 05:14:42 - src.bot - ERROR - _initialize_bot_data:115 - Failed to initialize bot data: Database objects do not implement truth value testing or bool(). Please compare with None instead: database is not None
2025-07-01 05:14:42 - src.bot - ERROR - _initialize_bot_data:115 - Failed to initialize bot data: Database objects do not implement truth value testing or bool(). Please compare with None instead: database is not None
2025-07-01 05:14:42 - src.bot - INFO - _start_polling:139 - Starting bot in long polling mode...
2025-07-01 05:14:42 - src.bot - INFO - _start_polling:139 - Starting bot in long polling mode...
2025-07-01 05:14:42 - src.bot - INFO - _start_polling:140 - Bot is running in long polling mode. Press Ctrl+C to stop.
2025-07-01 05:14:42 - src.bot - INFO - _start_polling:140 - Bot is running in long polling mode. Press Ctrl+C to stop.
2025-07-01 05:14:42 - src.bot - INFO - _start_polling:141 - No webhook, domain, or SSL configuration required.
2025-07-01 05:14:42 - src.bot - INFO - _start_polling:141 - No webhook, domain, or SSL configuration required.
2025-07-01 05:14:42 - src.bot - ERROR - start:67 - Failed to start bot: Cannot close a running event loop
2025-07-01 05:14:42 - src.bot - ERROR - start:67 - Failed to start bot: Cannot close a running event loop
2025-07-01 05:24:43 - __main__ - INFO - initialize:42 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-01 05:24:43 - __main__ - INFO - initialize:43 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-01 05:24:43 - __main__ - INFO - initialize:47 - ✅ Configuration validated
2025-07-01 05:24:51 - __main__ - INFO - initialize:52 - ✅ Database connected successfully
2025-07-01 05:24:51 - __main__ - INFO - initialize:63 - ✅ Services initialized
2025-07-01 05:24:51 - __main__ - INFO - initialize:68 - ✅ Default products initialized
2025-07-01 05:24:52 - __main__ - INFO - initialize:77 - ✅ All handlers added successfully
2025-07-01 05:24:52 - __main__ - INFO - run:1271 - 🚀 Starting bot in long polling mode...
2025-07-01 05:24:52 - __main__ - INFO - run:1272 - ✅ Bot is running! Press Ctrl+C to stop.
2025-07-01 05:24:52 - __main__ - INFO - run:1273 - ℹ️ No webhook, domain, or SSL configuration required.
2025-07-01 05:24:52 - __main__ - INFO - run:1274 - 🤖 Bot username: @pro_gifts_bot
2025-07-01 05:24:52 - __main__ - ERROR - run:1290 - ❌ Bot error: Cannot close a running event loop
2025-07-01 05:27:17 - __main__ - INFO - main:905 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-01 05:27:17 - __main__ - INFO - main:906 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-01 05:27:17 - __main__ - INFO - main:910 - ✅ Configuration validated
2025-07-01 05:27:18 - __main__ - INFO - main:927 - ✅ All handlers added successfully
2025-07-01 05:27:18 - __main__ - INFO - initialize_async_components:42 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-01 05:27:18 - __main__ - INFO - initialize_async_components:43 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-01 05:27:18 - __main__ - INFO - initialize_async_components:47 - ✅ Configuration validated
2025-07-01 05:27:25 - __main__ - INFO - initialize_async_components:52 - ✅ Database connected successfully
2025-07-01 05:27:25 - __main__ - INFO - initialize_async_components:63 - ✅ Services initialized
2025-07-01 05:27:25 - __main__ - INFO - initialize_async_components:68 - ✅ Default products initialized
2025-07-01 05:27:25 - __main__ - INFO - init_and_run:932 - 🚀 Starting bot in long polling mode...
2025-07-01 05:27:25 - __main__ - INFO - init_and_run:933 - ✅ Bot is running! Press Ctrl+C to stop.
2025-07-01 05:27:25 - __main__ - INFO - init_and_run:934 - ℹ️ No webhook, domain, or SSL configuration required.
2025-07-01 05:27:25 - __main__ - INFO - init_and_run:935 - 🤖 Bot username: @pro_gifts_bot
2025-07-01 05:34:07 - __main__ - INFO - main:1203 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-01 05:34:07 - __main__ - INFO - main:1204 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-01 05:34:07 - __main__ - INFO - main:1208 - ✅ Configuration validated
2025-07-01 05:34:08 - __main__ - INFO - main:1225 - ✅ All handlers added successfully
2025-07-01 05:34:08 - __main__ - INFO - initialize_async_components:42 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-01 05:34:08 - __main__ - INFO - initialize_async_components:43 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-01 05:34:08 - __main__ - INFO - initialize_async_components:47 - ✅ Configuration validated
2025-07-01 05:34:16 - __main__ - INFO - initialize_async_components:52 - ✅ Database connected successfully
2025-07-01 05:34:16 - __main__ - INFO - initialize_async_components:63 - ✅ Services initialized
2025-07-01 05:34:16 - __main__ - INFO - initialize_async_components:68 - ✅ Default products initialized
2025-07-01 05:34:16 - __main__ - INFO - init_and_run:1230 - 🚀 Starting bot in long polling mode...
2025-07-01 05:34:16 - __main__ - INFO - init_and_run:1231 - ✅ Bot is running! Press Ctrl+C to stop.
2025-07-01 05:34:16 - __main__ - INFO - init_and_run:1232 - ℹ️ No webhook, domain, or SSL configuration required.
2025-07-01 05:34:16 - __main__ - INFO - init_and_run:1233 - 🤖 Bot username: @pro_gifts_bot
2025-07-01 05:44:52 - __main__ - INFO - main:1112 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-01 05:44:52 - __main__ - INFO - main:1113 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-01 05:44:52 - __main__ - INFO - main:1117 - ✅ Configuration validated
2025-07-01 05:44:53 - __main__ - INFO - main:1134 - ✅ All handlers added successfully
2025-07-01 05:44:53 - __main__ - INFO - initialize_async_components:42 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-01 05:44:53 - __main__ - INFO - initialize_async_components:43 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-01 05:44:53 - __main__ - INFO - initialize_async_components:47 - ✅ Configuration validated
2025-07-01 05:45:01 - __main__ - INFO - initialize_async_components:52 - ✅ Database connected successfully
2025-07-01 05:45:01 - __main__ - INFO - initialize_async_components:63 - ✅ Services initialized
2025-07-01 05:45:01 - __main__ - INFO - initialize_async_components:68 - ✅ Default products initialized
2025-07-01 05:45:01 - __main__ - INFO - init_and_run:1139 - 🚀 Starting bot in long polling mode...
2025-07-01 05:45:01 - __main__ - INFO - init_and_run:1140 - ✅ Bot is running! Press Ctrl+C to stop.
2025-07-01 05:45:01 - __main__ - INFO - init_and_run:1141 - ℹ️ No webhook, domain, or SSL configuration required.
2025-07-01 05:45:01 - __main__ - INFO - init_and_run:1142 - 🤖 Bot username: @pro_gifts_bot
2025-07-01 05:49:57 - __main__ - INFO - main:1112 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-01 05:49:57 - __main__ - INFO - main:1113 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-01 05:49:57 - __main__ - INFO - main:1117 - ✅ Configuration validated
2025-07-01 05:49:58 - __main__ - INFO - main:1134 - ✅ All handlers added successfully
2025-07-01 05:49:58 - __main__ - INFO - initialize_async_components:42 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-01 05:49:58 - __main__ - INFO - initialize_async_components:43 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-01 05:49:58 - __main__ - INFO - initialize_async_components:47 - ✅ Configuration validated
2025-07-01 05:50:05 - __main__ - INFO - initialize_async_components:52 - ✅ Database connected successfully
2025-07-01 05:50:05 - __main__ - INFO - initialize_async_components:63 - ✅ Services initialized
2025-07-01 05:50:05 - __main__ - INFO - initialize_async_components:68 - ✅ Default products initialized
2025-07-01 05:50:05 - __main__ - INFO - init_and_run:1139 - 🚀 Starting bot in long polling mode...
2025-07-01 05:50:05 - __main__ - INFO - init_and_run:1140 - ✅ Bot is running! Press Ctrl+C to stop.
2025-07-01 05:50:05 - __main__ - INFO - init_and_run:1141 - ℹ️ No webhook, domain, or SSL configuration required.
2025-07-01 05:50:05 - __main__ - INFO - init_and_run:1142 - 🤖 Bot username: @pro_gifts_bot
2025-07-01 05:55:18 - __main__ - INFO - main:1112 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-01 05:55:18 - __main__ - INFO - main:1113 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-01 05:55:18 - __main__ - INFO - main:1117 - ✅ Configuration validated
2025-07-01 05:55:19 - __main__ - INFO - main:1134 - ✅ All handlers added successfully
2025-07-01 05:55:19 - __main__ - INFO - initialize_async_components:42 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-01 05:55:19 - __main__ - INFO - initialize_async_components:43 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-01 05:55:19 - __main__ - INFO - initialize_async_components:47 - ✅ Configuration validated
2025-07-01 05:55:26 - __main__ - INFO - initialize_async_components:52 - ✅ Database connected successfully
2025-07-01 05:55:26 - __main__ - INFO - initialize_async_components:63 - ✅ Services initialized
2025-07-01 05:55:26 - __main__ - INFO - initialize_async_components:68 - ✅ Default products initialized
2025-07-01 05:55:26 - __main__ - INFO - init_and_run:1139 - 🚀 Starting bot in long polling mode...
2025-07-01 05:55:26 - __main__ - INFO - init_and_run:1140 - ✅ Bot is running! Press Ctrl+C to stop.
2025-07-01 05:55:26 - __main__ - INFO - init_and_run:1141 - ℹ️ No webhook, domain, or SSL configuration required.
2025-07-01 05:55:26 - __main__ - INFO - init_and_run:1142 - 🤖 Bot username: @pro_gifts_bot
2025-07-01 06:28:56 - __main__ - ERROR - error_handler:1095 - Update None caused error httpx.ConnectError: [Errno 11001] getaddrinfo failed
2025-07-01 06:28:57 - __main__ - ERROR - error_handler:1095 - Update None caused error httpx.ConnectError: [Errno 11001] getaddrinfo failed
2025-07-01 06:28:59 - __main__ - ERROR - error_handler:1095 - Update None caused error httpx.ConnectError: [Errno 11001] getaddrinfo failed
2025-07-01 06:29:01 - __main__ - ERROR - error_handler:1095 - Update None caused error httpx.ConnectError: [Errno 11001] getaddrinfo failed
2025-07-02 09:27:46 - __main__ - ERROR - error_handler:1095 - Update None caused error httpx.ConnectError: [Errno 11001] getaddrinfo failed
2025-07-06 23:20:14 - src - INFO - main:29 - Starting Telegram Referral Earning Bot...
2025-07-06 23:20:14 - src - INFO - main:30 - Mode: Long Polling (no webhook/domain required)
2025-07-06 23:20:22 - src.database - INFO - _initialize_collections:97 - Database collections and indexes initialized
2025-07-06 23:20:22 - src.database - INFO - connect:35 - Successfully connected to MongoDB Atlas
2025-07-06 23:20:22 - src - INFO - main:35 - Database connected successfully
2025-07-06 23:20:22 - src.bot - INFO - __init__:49 - ReferralBot initialized
2025-07-06 23:20:22 - src.bot - INFO - __init__:49 - ReferralBot initialized
2025-07-06 23:59:06 - __main__ - INFO - main:1159 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-06 23:59:06 - __main__ - INFO - main:1160 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-06 23:59:06 - __main__ - INFO - main:1164 - ✅ Configuration validated
2025-07-06 23:59:07 - __main__ - INFO - main:1181 - ✅ All handlers added successfully
2025-07-06 23:59:07 - __main__ - INFO - initialize_async_components:43 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-06 23:59:07 - __main__ - INFO - initialize_async_components:44 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-06 23:59:07 - __main__ - INFO - initialize_async_components:48 - ✅ Configuration validated
2025-07-06 23:59:15 - __main__ - INFO - initialize_async_components:53 - ✅ Database connected successfully
2025-07-06 23:59:15 - __main__ - INFO - initialize_async_components:64 - ✅ Services initialized
2025-07-06 23:59:15 - __main__ - INFO - initialize_async_components:69 - ✅ Default products initialized
2025-07-06 23:59:15 - __main__ - INFO - init_and_run:1186 - 🚀 Starting bot in long polling mode...
2025-07-06 23:59:15 - __main__ - INFO - init_and_run:1187 - ✅ Bot is running! Press Ctrl+C to stop.
2025-07-06 23:59:15 - __main__ - INFO - init_and_run:1188 - ℹ️ No webhook, domain, or SSL configuration required.
2025-07-06 23:59:15 - __main__ - INFO - init_and_run:1189 - 🤖 Bot username: @pro_gifts_bot
2025-07-06 23:59:20 - __main__ - ERROR - main:1205 - ❌ Bot error: Timed out
2025-07-07 00:09:32 - __main__ - INFO - main:1598 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-07 00:09:32 - __main__ - INFO - main:1599 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-07 00:09:32 - __main__ - INFO - main:1603 - ✅ Configuration validated
2025-07-07 00:09:33 - __main__ - INFO - main:1625 - ✅ All handlers added successfully
2025-07-07 00:09:33 - __main__ - INFO - initialize_async_components:44 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-07 00:09:33 - __main__ - INFO - initialize_async_components:45 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-07 00:09:33 - __main__ - INFO - initialize_async_components:49 - ✅ Configuration validated
2025-07-07 00:09:40 - __main__ - INFO - initialize_async_components:54 - ✅ Database connected successfully
2025-07-07 00:09:40 - __main__ - INFO - initialize_async_components:66 - ✅ Services initialized
2025-07-07 00:09:40 - __main__ - INFO - initialize_async_components:71 - ✅ Default products initialized
2025-07-07 00:09:40 - __main__ - INFO - init_and_run:1630 - 🚀 Starting bot in long polling mode...
2025-07-07 00:09:40 - __main__ - INFO - init_and_run:1631 - ✅ Bot is running! Press Ctrl+C to stop.
2025-07-07 00:09:40 - __main__ - INFO - init_and_run:1632 - ℹ️ No webhook, domain, or SSL configuration required.
2025-07-07 00:09:40 - __main__ - INFO - init_and_run:1633 - 🤖 Bot username: @pro_gifts_bot
2025-07-07 00:12:41 - __main__ - INFO - main:1598 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-07 00:12:41 - __main__ - INFO - main:1599 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-07 00:12:41 - __main__ - INFO - main:1603 - ✅ Configuration validated
2025-07-07 00:12:42 - __main__ - INFO - main:1625 - ✅ All handlers added successfully
2025-07-07 00:12:42 - __main__ - INFO - initialize_async_components:44 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-07 00:12:42 - __main__ - INFO - initialize_async_components:45 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-07 00:12:42 - __main__ - INFO - initialize_async_components:49 - ✅ Configuration validated
2025-07-07 00:12:48 - __main__ - INFO - main:1647 - 👋 Bot stopped by user
2025-07-07 00:13:53 - __main__ - INFO - main:1598 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-07 00:13:53 - __main__ - INFO - main:1599 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-07 00:13:53 - __main__ - INFO - main:1603 - ✅ Configuration validated
2025-07-07 00:13:54 - __main__ - INFO - main:1625 - ✅ All handlers added successfully
2025-07-07 00:13:54 - __main__ - INFO - initialize_async_components:44 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-07 00:13:54 - __main__ - INFO - initialize_async_components:45 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-07 00:13:54 - __main__ - INFO - initialize_async_components:49 - ✅ Configuration validated
2025-07-07 00:14:01 - __main__ - INFO - initialize_async_components:54 - ✅ Database connected successfully
2025-07-07 00:14:01 - __main__ - INFO - initialize_async_components:66 - ✅ Services initialized
2025-07-07 00:14:01 - __main__ - INFO - initialize_async_components:71 - ✅ Default products initialized
2025-07-07 00:14:01 - __main__ - INFO - init_and_run:1630 - 🚀 Starting bot in long polling mode...
2025-07-07 00:14:01 - __main__ - INFO - init_and_run:1631 - ✅ Bot is running! Press Ctrl+C to stop.
2025-07-07 00:14:01 - __main__ - INFO - init_and_run:1632 - ℹ️ No webhook, domain, or SSL configuration required.
2025-07-07 00:14:01 - __main__ - INFO - init_and_run:1633 - 🤖 Bot username: @pro_gifts_bot
2025-07-07 00:16:48 - __main__ - INFO - main:1598 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-07 00:16:48 - __main__ - INFO - main:1599 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-07 00:16:48 - __main__ - INFO - main:1603 - ✅ Configuration validated
2025-07-07 00:16:48 - __main__ - INFO - main:1625 - ✅ All handlers added successfully
2025-07-07 00:16:48 - __main__ - INFO - initialize_async_components:44 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-07 00:16:48 - __main__ - INFO - initialize_async_components:45 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-07 00:16:48 - __main__ - INFO - initialize_async_components:49 - ✅ Configuration validated
2025-07-07 00:16:55 - __main__ - INFO - initialize_async_components:54 - ✅ Database connected successfully
2025-07-07 00:16:55 - __main__ - INFO - initialize_async_components:66 - ✅ Services initialized
2025-07-07 00:16:55 - __main__ - INFO - initialize_async_components:71 - ✅ Default products initialized
2025-07-07 00:16:55 - __main__ - INFO - init_and_run:1630 - 🚀 Starting bot in long polling mode...
2025-07-07 00:16:55 - __main__ - INFO - init_and_run:1631 - ✅ Bot is running! Press Ctrl+C to stop.
2025-07-07 00:16:55 - __main__ - INFO - init_and_run:1632 - ℹ️ No webhook, domain, or SSL configuration required.
2025-07-07 00:16:55 - __main__ - INFO - init_and_run:1633 - 🤖 Bot username: @pro_gifts_bot
2025-07-07 00:20:19 - __main__ - INFO - main:1598 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-07 00:20:19 - __main__ - INFO - main:1599 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-07 00:20:19 - __main__ - INFO - main:1603 - ✅ Configuration validated
2025-07-07 00:20:20 - __main__ - INFO - main:1625 - ✅ All handlers added successfully
2025-07-07 00:20:20 - __main__ - INFO - initialize_async_components:44 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-07 00:20:20 - __main__ - INFO - initialize_async_components:45 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-07 00:20:20 - __main__ - INFO - initialize_async_components:49 - ✅ Configuration validated
2025-07-07 00:20:26 - __main__ - INFO - initialize_async_components:54 - ✅ Database connected successfully
2025-07-07 00:20:26 - __main__ - INFO - initialize_async_components:66 - ✅ Services initialized
2025-07-07 00:20:26 - __main__ - INFO - initialize_async_components:71 - ✅ Default products initialized
2025-07-07 00:20:26 - __main__ - INFO - init_and_run:1630 - 🚀 Starting bot in long polling mode...
2025-07-07 00:20:26 - __main__ - INFO - init_and_run:1631 - ✅ Bot is running! Press Ctrl+C to stop.
2025-07-07 00:20:26 - __main__ - INFO - init_and_run:1632 - ℹ️ No webhook, domain, or SSL configuration required.
2025-07-07 00:20:26 - __main__ - INFO - init_and_run:1633 - 🤖 Bot username: @pro_gifts_bot
2025-07-07 00:20:28 - __main__ - ERROR - error_handler:1581 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-07 00:20:32 - __main__ - ERROR - error_handler:1581 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-07 00:21:39 - __main__ - INFO - main:1598 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-07 00:21:39 - __main__ - INFO - main:1599 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-07 00:21:39 - __main__ - INFO - main:1603 - ✅ Configuration validated
2025-07-07 00:21:40 - __main__ - INFO - main:1625 - ✅ All handlers added successfully
2025-07-07 00:21:40 - __main__ - INFO - initialize_async_components:44 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-07 00:21:40 - __main__ - INFO - initialize_async_components:45 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-07 00:21:40 - __main__ - INFO - initialize_async_components:49 - ✅ Configuration validated
2025-07-07 00:21:48 - __main__ - INFO - initialize_async_components:54 - ✅ Database connected successfully
2025-07-07 00:21:48 - __main__ - INFO - initialize_async_components:66 - ✅ Services initialized
2025-07-07 00:21:48 - __main__ - INFO - initialize_async_components:71 - ✅ Default products initialized
2025-07-07 00:21:48 - __main__ - INFO - init_and_run:1630 - 🚀 Starting bot in long polling mode...
2025-07-07 00:21:48 - __main__ - INFO - init_and_run:1631 - ✅ Bot is running! Press Ctrl+C to stop.
2025-07-07 00:21:48 - __main__ - INFO - init_and_run:1632 - ℹ️ No webhook, domain, or SSL configuration required.
2025-07-07 00:21:48 - __main__ - INFO - init_and_run:1633 - 🤖 Bot username: @pro_gifts_bot
2025-07-07 00:26:46 - __main__ - INFO - main:1598 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-07 00:26:46 - __main__ - INFO - main:1599 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-07 00:26:46 - __main__ - INFO - main:1603 - ✅ Configuration validated
2025-07-07 00:26:47 - __main__ - INFO - main:1625 - ✅ All handlers added successfully
2025-07-07 00:26:47 - __main__ - INFO - initialize_async_components:44 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-07 00:26:47 - __main__ - INFO - initialize_async_components:45 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-07 00:26:47 - __main__ - INFO - initialize_async_components:49 - ✅ Configuration validated
2025-07-07 00:26:53 - __main__ - INFO - initialize_async_components:54 - ✅ Database connected successfully
2025-07-07 00:26:53 - __main__ - INFO - initialize_async_components:66 - ✅ Services initialized
2025-07-07 00:26:54 - __main__ - INFO - initialize_async_components:71 - ✅ Default products initialized
2025-07-07 00:26:54 - __main__ - INFO - init_and_run:1630 - 🚀 Starting bot in long polling mode...
2025-07-07 00:26:54 - __main__ - INFO - init_and_run:1631 - ✅ Bot is running! Press Ctrl+C to stop.
2025-07-07 00:26:54 - __main__ - INFO - init_and_run:1632 - ℹ️ No webhook, domain, or SSL configuration required.
2025-07-07 00:26:54 - __main__ - INFO - init_and_run:1633 - 🤖 Bot username: @pro_gifts_bot
2025-07-07 00:27:07 - __main__ - ERROR - balance_command:413 - Error in balance command: 'int' object has no attribute 'strftime'
2025-07-07 00:33:26 - __main__ - INFO - main:1629 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-07 00:33:26 - __main__ - INFO - main:1630 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-07 00:33:26 - __main__ - INFO - main:1634 - ✅ Configuration validated
2025-07-07 00:33:27 - __main__ - INFO - main:1656 - ✅ All handlers added successfully
2025-07-07 00:33:27 - __main__ - INFO - initialize_async_components:44 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-07 00:33:27 - __main__ - INFO - initialize_async_components:45 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-07 00:33:27 - __main__ - INFO - initialize_async_components:49 - ✅ Configuration validated
2025-07-07 00:33:34 - __main__ - INFO - initialize_async_components:54 - ✅ Database connected successfully
2025-07-07 00:33:34 - __main__ - INFO - initialize_async_components:66 - ✅ Services initialized
2025-07-07 00:33:34 - __main__ - INFO - initialize_async_components:71 - ✅ Default products initialized
2025-07-07 00:33:34 - __main__ - INFO - init_and_run:1661 - 🚀 Starting bot in long polling mode...
2025-07-07 00:33:34 - __main__ - INFO - init_and_run:1662 - ✅ Bot is running! Press Ctrl+C to stop.
2025-07-07 00:33:34 - __main__ - INFO - init_and_run:1663 - ℹ️ No webhook, domain, or SSL configuration required.
2025-07-07 00:33:34 - __main__ - INFO - init_and_run:1664 - 🤖 Bot username: @pro_gifts_bot
2025-07-07 00:35:09 - __main__ - INFO - main:1629 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-07 00:35:09 - __main__ - INFO - main:1630 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-07 00:35:09 - __main__ - INFO - main:1634 - ✅ Configuration validated
2025-07-07 00:35:09 - __main__ - INFO - main:1656 - ✅ All handlers added successfully
2025-07-07 00:35:09 - __main__ - INFO - initialize_async_components:44 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-07 00:35:09 - __main__ - INFO - initialize_async_components:45 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-07 00:35:09 - __main__ - INFO - initialize_async_components:49 - ✅ Configuration validated
2025-07-07 00:35:16 - __main__ - INFO - initialize_async_components:54 - ✅ Database connected successfully
2025-07-07 00:35:16 - __main__ - INFO - initialize_async_components:66 - ✅ Services initialized
2025-07-07 00:35:16 - __main__ - INFO - initialize_async_components:71 - ✅ Default products initialized
2025-07-07 00:35:16 - __main__ - INFO - init_and_run:1661 - 🚀 Starting bot in long polling mode...
2025-07-07 00:35:16 - __main__ - INFO - init_and_run:1662 - ✅ Bot is running! Press Ctrl+C to stop.
2025-07-07 00:35:16 - __main__ - INFO - init_and_run:1663 - ℹ️ No webhook, domain, or SSL configuration required.
2025-07-07 00:35:16 - __main__ - INFO - init_and_run:1664 - 🤖 Bot username: @pro_gifts_bot
2025-07-07 00:52:38 - __main__ - INFO - main:1519 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-07 00:52:38 - __main__ - INFO - main:1520 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-07 00:52:38 - __main__ - INFO - main:1524 - ✅ Configuration validated
2025-07-07 00:52:38 - __main__ - INFO - main:1546 - ✅ All handlers added successfully
2025-07-07 00:52:38 - __main__ - INFO - initialize_async_components:44 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-07 00:52:38 - __main__ - INFO - initialize_async_components:45 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-07 00:52:38 - __main__ - INFO - initialize_async_components:49 - ✅ Configuration validated
2025-07-07 00:52:45 - __main__ - INFO - initialize_async_components:54 - ✅ Database connected successfully
2025-07-07 00:52:45 - __main__ - INFO - initialize_async_components:66 - ✅ Services initialized
2025-07-07 00:52:45 - __main__ - INFO - initialize_async_components:71 - ✅ Default products initialized
2025-07-07 00:52:45 - __main__ - INFO - init_and_run:1551 - 🚀 Starting bot in long polling mode...
2025-07-07 00:52:45 - __main__ - INFO - init_and_run:1552 - ✅ Bot is running! Press Ctrl+C to stop.
2025-07-07 00:52:45 - __main__ - INFO - init_and_run:1553 - ℹ️ No webhook, domain, or SSL configuration required.
2025-07-07 00:52:45 - __main__ - INFO - init_and_run:1554 - 🤖 Bot username: @pro_gifts_bot
2025-07-07 00:52:47 - __main__ - ERROR - error_handler:1612 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-07 00:52:51 - __main__ - ERROR - error_handler:1502 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-07 00:56:01 - __main__ - INFO - main:1519 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-07 00:56:01 - __main__ - INFO - main:1520 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-07 00:56:01 - __main__ - INFO - main:1524 - ✅ Configuration validated
2025-07-07 00:56:02 - __main__ - INFO - main:1546 - ✅ All handlers added successfully
2025-07-07 00:56:02 - __main__ - INFO - initialize_async_components:44 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-07 00:56:02 - __main__ - INFO - initialize_async_components:45 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-07 00:56:02 - __main__ - INFO - initialize_async_components:49 - ✅ Configuration validated
2025-07-07 00:56:09 - __main__ - INFO - initialize_async_components:54 - ✅ Database connected successfully
2025-07-07 00:56:09 - __main__ - INFO - initialize_async_components:66 - ✅ Services initialized
2025-07-07 00:56:09 - __main__ - INFO - initialize_async_components:71 - ✅ Default products initialized
2025-07-07 00:56:09 - __main__ - INFO - init_and_run:1551 - 🚀 Starting bot in long polling mode...
2025-07-07 00:56:09 - __main__ - INFO - init_and_run:1552 - ✅ Bot is running! Press Ctrl+C to stop.
2025-07-07 00:56:09 - __main__ - INFO - init_and_run:1553 - ℹ️ No webhook, domain, or SSL configuration required.
2025-07-07 00:56:09 - __main__ - INFO - init_and_run:1554 - 🤖 Bot username: @pro_gifts_bot
2025-07-07 12:59:18 - __main__ - INFO - main:1519 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-07 12:59:18 - __main__ - INFO - main:1520 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-07 12:59:18 - __main__ - INFO - main:1524 - ✅ Configuration validated
2025-07-07 12:59:20 - __main__ - INFO - main:1546 - ✅ All handlers added successfully
2025-07-07 12:59:20 - __main__ - INFO - initialize_async_components:44 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-07 12:59:20 - __main__ - INFO - initialize_async_components:45 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-07 12:59:20 - __main__ - INFO - initialize_async_components:49 - ✅ Configuration validated
2025-07-07 12:59:28 - __main__ - INFO - initialize_async_components:54 - ✅ Database connected successfully
2025-07-07 12:59:28 - __main__ - INFO - initialize_async_components:66 - ✅ Services initialized
2025-07-07 12:59:28 - __main__ - INFO - initialize_async_components:71 - ✅ Default products initialized
2025-07-07 12:59:28 - __main__ - INFO - init_and_run:1551 - 🚀 Starting bot in long polling mode...
2025-07-07 12:59:28 - __main__ - INFO - init_and_run:1552 - ✅ Bot is running! Press Ctrl+C to stop.
2025-07-07 12:59:28 - __main__ - INFO - init_and_run:1553 - ℹ️ No webhook, domain, or SSL configuration required.
2025-07-07 12:59:28 - __main__ - INFO - init_and_run:1554 - 🤖 Bot username: @pro_gifts_bot
2025-07-07 13:04:38 - __main__ - ERROR - _handle_tasks_menu:858 - Error in tasks menu: Can't parse entities: can't find end of the entity starting at byte offset 978
2025-07-07 13:05:46 - __main__ - ERROR - _handle_tasks_menu:858 - Error in tasks menu: Can't parse entities: can't find end of the entity starting at byte offset 978
2025-07-07 13:17:43 - __main__ - INFO - main:1436 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-07 13:17:43 - __main__ - INFO - main:1437 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-07 13:17:43 - __main__ - INFO - main:1441 - ✅ Configuration validated
2025-07-07 13:17:44 - __main__ - INFO - main:1463 - ✅ All handlers added successfully
2025-07-07 13:17:44 - __main__ - INFO - initialize_async_components:44 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-07 13:17:44 - __main__ - INFO - initialize_async_components:45 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-07 13:17:44 - __main__ - INFO - initialize_async_components:49 - ✅ Configuration validated
2025-07-07 13:17:52 - __main__ - INFO - initialize_async_components:54 - ✅ Database connected successfully
2025-07-07 13:17:52 - __main__ - INFO - initialize_async_components:66 - ✅ Services initialized
2025-07-07 13:17:53 - __main__ - INFO - initialize_async_components:71 - ✅ Default products initialized
2025-07-07 13:17:53 - __main__ - INFO - init_and_run:1468 - 🚀 Starting bot in long polling mode...
2025-07-07 13:17:53 - __main__ - INFO - init_and_run:1469 - ✅ Bot is running! Press Ctrl+C to stop.
2025-07-07 13:17:53 - __main__ - INFO - init_and_run:1470 - ℹ️ No webhook, domain, or SSL configuration required.
2025-07-07 13:17:53 - __main__ - INFO - init_and_run:1471 - 🤖 Bot username: @pro_gifts_bot
2025-07-07 13:17:54 - __main__ - ERROR - error_handler:1502 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-07 13:17:58 - __main__ - ERROR - error_handler:1419 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-07 13:17:59 - __main__ - ERROR - error_handler:1502 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-07 13:18:04 - __main__ - ERROR - error_handler:1419 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-07 13:18:06 - __main__ - ERROR - error_handler:1502 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-07 13:18:11 - __main__ - ERROR - error_handler:1419 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-07 13:18:14 - __main__ - ERROR - error_handler:1502 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-07 13:18:17 - __main__ - ERROR - error_handler:1419 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-07 13:18:21 - __main__ - ERROR - error_handler:1502 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-07 13:18:37 - __main__ - INFO - main:1436 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-07 13:18:37 - __main__ - INFO - main:1437 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-07 13:18:37 - __main__ - INFO - main:1441 - ✅ Configuration validated
2025-07-07 13:18:38 - __main__ - INFO - main:1463 - ✅ All handlers added successfully
2025-07-07 13:18:38 - __main__ - INFO - initialize_async_components:44 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-07 13:18:38 - __main__ - INFO - initialize_async_components:45 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-07 13:18:38 - __main__ - INFO - initialize_async_components:49 - ✅ Configuration validated
2025-07-07 13:18:45 - __main__ - INFO - initialize_async_components:54 - ✅ Database connected successfully
2025-07-07 13:18:45 - __main__ - INFO - initialize_async_components:66 - ✅ Services initialized
2025-07-07 13:18:45 - __main__ - INFO - initialize_async_components:71 - ✅ Default products initialized
2025-07-07 13:18:45 - __main__ - INFO - init_and_run:1468 - 🚀 Starting bot in long polling mode...
2025-07-07 13:18:45 - __main__ - INFO - init_and_run:1469 - ✅ Bot is running! Press Ctrl+C to stop.
2025-07-07 13:18:45 - __main__ - INFO - init_and_run:1470 - ℹ️ No webhook, domain, or SSL configuration required.
2025-07-07 13:18:45 - __main__ - INFO - init_and_run:1471 - 🤖 Bot username: @pro_gifts_bot
2025-07-07 13:18:46 - __main__ - ERROR - error_handler:1502 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-07 13:18:50 - __main__ - ERROR - error_handler:1419 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-07 13:18:51 - __main__ - ERROR - error_handler:1502 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-07 13:18:56 - __main__ - ERROR - error_handler:1419 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-07 13:18:58 - __main__ - ERROR - error_handler:1502 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-07 13:19:03 - __main__ - ERROR - error_handler:1419 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-07 13:20:17 - __main__ - ERROR - _handle_tasks_menu:858 - Error in tasks menu: Can't parse entities: can't find end of the entity starting at byte offset 978
2025-07-07 13:30:34 - __main__ - INFO - main:1993 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-07 13:30:34 - __main__ - INFO - main:1994 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-07 13:30:34 - __main__ - INFO - main:1998 - ✅ Configuration validated
2025-07-07 13:30:34 - __main__ - INFO - main:2021 - ✅ All handlers added successfully
2025-07-07 13:30:34 - __main__ - INFO - initialize_async_components:45 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-07 13:30:34 - __main__ - INFO - initialize_async_components:46 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-07 13:30:34 - __main__ - INFO - initialize_async_components:50 - ✅ Configuration validated
2025-07-07 13:30:42 - __main__ - INFO - initialize_async_components:55 - ✅ Database connected successfully
2025-07-07 13:30:42 - __main__ - INFO - initialize_async_components:68 - ✅ Services initialized
2025-07-07 13:30:42 - __main__ - INFO - initialize_async_components:73 - ✅ Default products initialized
2025-07-07 13:30:42 - __main__ - INFO - init_and_run:2026 - 🚀 Starting bot in long polling mode...
2025-07-07 13:30:42 - __main__ - INFO - init_and_run:2027 - ✅ Bot is running! Press Ctrl+C to stop.
2025-07-07 13:30:42 - __main__ - INFO - init_and_run:2028 - ℹ️ No webhook, domain, or SSL configuration required.
2025-07-07 13:30:42 - __main__ - INFO - init_and_run:2029 - 🤖 Bot username: @pro_gifts_bot
2025-07-07 13:30:45 - __main__ - ERROR - error_handler:1502 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-07 13:30:49 - __main__ - ERROR - error_handler:1976 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-07 13:30:50 - __main__ - ERROR - error_handler:1502 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-07 13:30:55 - __main__ - ERROR - error_handler:1976 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-07 13:30:56 - __main__ - ERROR - error_handler:1502 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-07 13:37:49 - __main__ - INFO - main:2828 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-07 13:37:49 - __main__ - INFO - main:2829 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-07 13:37:49 - __main__ - INFO - main:2833 - ✅ Configuration validated
2025-07-07 13:37:49 - __main__ - INFO - main:2856 - ✅ All handlers added successfully
2025-07-07 13:37:49 - __main__ - INFO - initialize_async_components:45 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-07 13:37:49 - __main__ - INFO - initialize_async_components:46 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-07 13:37:49 - __main__ - INFO - initialize_async_components:50 - ✅ Configuration validated
2025-07-07 13:37:56 - __main__ - INFO - initialize_async_components:55 - ✅ Database connected successfully
2025-07-07 13:37:56 - __main__ - INFO - initialize_async_components:68 - ✅ Services initialized
2025-07-07 13:37:56 - __main__ - INFO - initialize_async_components:73 - ✅ Default products initialized
2025-07-07 13:37:56 - __main__ - INFO - init_and_run:2861 - 🚀 Starting bot in long polling mode...
2025-07-07 13:37:56 - __main__ - INFO - init_and_run:2862 - ✅ Bot is running! Press Ctrl+C to stop.
2025-07-07 13:37:56 - __main__ - INFO - init_and_run:2863 - ℹ️ No webhook, domain, or SSL configuration required.
2025-07-07 13:37:56 - __main__ - INFO - init_and_run:2864 - 🤖 Bot username: @pro_gifts_bot
2025-07-07 13:43:08 - __main__ - INFO - main:2856 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-07 13:43:08 - __main__ - INFO - main:2857 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-07 13:43:08 - __main__ - INFO - main:2861 - ✅ Configuration validated
2025-07-07 13:43:08 - __main__ - INFO - main:2884 - ✅ All handlers added successfully
2025-07-07 13:43:08 - __main__ - INFO - initialize_async_components:45 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-07 13:43:08 - __main__ - INFO - initialize_async_components:46 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-07 13:43:08 - __main__ - INFO - initialize_async_components:50 - ✅ Configuration validated
2025-07-07 13:43:16 - __main__ - INFO - initialize_async_components:55 - ✅ Database connected successfully
2025-07-07 13:43:16 - __main__ - INFO - initialize_async_components:68 - ✅ Services initialized
2025-07-07 13:43:16 - __main__ - INFO - initialize_async_components:73 - ✅ Default products initialized
2025-07-07 13:43:16 - __main__ - INFO - init_and_run:2889 - 🚀 Starting bot in long polling mode...
2025-07-07 13:43:16 - __main__ - INFO - init_and_run:2890 - ✅ Bot is running! Press Ctrl+C to stop.
2025-07-07 13:43:16 - __main__ - INFO - init_and_run:2891 - ℹ️ No webhook, domain, or SSL configuration required.
2025-07-07 13:43:16 - __main__ - INFO - init_and_run:2892 - 🤖 Bot username: @pro_gifts_bot
2025-07-07 13:44:52 - __main__ - INFO - main:2856 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-07 13:44:52 - __main__ - INFO - main:2857 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-07 13:44:52 - __main__ - INFO - main:2861 - ✅ Configuration validated
2025-07-07 13:44:53 - __main__ - INFO - main:2884 - ✅ All handlers added successfully
2025-07-07 13:44:53 - __main__ - INFO - initialize_async_components:45 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-07 13:44:53 - __main__ - INFO - initialize_async_components:46 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-07 13:44:53 - __main__ - INFO - initialize_async_components:50 - ✅ Configuration validated
2025-07-07 13:45:01 - __main__ - INFO - initialize_async_components:55 - ✅ Database connected successfully
2025-07-07 13:45:01 - __main__ - INFO - initialize_async_components:68 - ✅ Services initialized
2025-07-07 13:45:01 - __main__ - INFO - initialize_async_components:73 - ✅ Default products initialized
2025-07-07 13:45:01 - __main__ - INFO - init_and_run:2889 - 🚀 Starting bot in long polling mode...
2025-07-07 13:45:01 - __main__ - INFO - init_and_run:2890 - ✅ Bot is running! Press Ctrl+C to stop.
2025-07-07 13:45:01 - __main__ - INFO - init_and_run:2891 - ℹ️ No webhook, domain, or SSL configuration required.
2025-07-07 13:45:01 - __main__ - INFO - init_and_run:2892 - 🤖 Bot username: @pro_gifts_bot
2025-07-07 13:45:57 - __main__ - ERROR - _show_bot_settings_menu:1756 - Error showing bot settings menu: 'CallbackQuery' object has no attribute 'callback_query'
2025-07-07 13:45:57 - __main__ - ERROR - _handle_admin_callbacks:1321 - Error in admin callback: 'CallbackQuery' object has no attribute 'callback_query'
2025-07-07 13:46:01 - __main__ - ERROR - _show_bot_settings_menu:1756 - Error showing bot settings menu: 'CallbackQuery' object has no attribute 'callback_query'
2025-07-07 13:46:01 - __main__ - ERROR - _handle_admin_callbacks:1321 - Error in admin callback: 'CallbackQuery' object has no attribute 'callback_query'
2025-07-07 13:46:02 - __main__ - ERROR - _show_broadcast_menu:1994 - Error showing broadcast menu: 'CallbackQuery' object has no attribute 'callback_query'
2025-07-07 13:46:02 - __main__ - ERROR - _handle_admin_callbacks:1321 - Error in admin callback: 'CallbackQuery' object has no attribute 'callback_query'
2025-07-07 13:46:03 - __main__ - ERROR - _show_task_management_menu:1945 - Error showing task management menu: 'CallbackQuery' object has no attribute 'callback_query'
2025-07-07 13:46:03 - __main__ - ERROR - _handle_admin_callbacks:1321 - Error in admin callback: 'CallbackQuery' object has no attribute 'callback_query'
2025-07-07 13:46:04 - __main__ - ERROR - _show_analytics_menu:1804 - Error showing analytics menu: 'CallbackQuery' object has no attribute 'callback_query'
2025-07-07 13:46:04 - __main__ - ERROR - _handle_admin_callbacks:1321 - Error in admin callback: 'CallbackQuery' object has no attribute 'callback_query'
2025-07-07 13:46:05 - __main__ - ERROR - _show_system_health:1847 - Error showing system health: 'CallbackQuery' object has no attribute 'callback_query'
2025-07-07 13:46:05 - __main__ - ERROR - _handle_admin_callbacks:1321 - Error in admin callback: 'CallbackQuery' object has no attribute 'callback_query'
2025-07-07 13:46:06 - __main__ - ERROR - _show_admin_logs:1905 - Error showing admin logs: object of type 'int' has no len()
2025-07-07 13:46:06 - __main__ - ERROR - _handle_admin_callbacks:1321 - Error in admin callback: 'CallbackQuery' object has no attribute 'callback_query'
2025-07-07 13:48:42 - __main__ - INFO - main:2856 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-07 13:48:42 - __main__ - INFO - main:2857 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-07 13:48:42 - __main__ - INFO - main:2861 - ✅ Configuration validated
2025-07-07 13:48:42 - __main__ - INFO - main:2884 - ✅ All handlers added successfully
2025-07-07 13:48:42 - __main__ - INFO - initialize_async_components:45 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-07 13:48:42 - __main__ - INFO - initialize_async_components:46 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-07 13:48:42 - __main__ - INFO - initialize_async_components:50 - ✅ Configuration validated
2025-07-07 13:48:50 - __main__ - INFO - initialize_async_components:55 - ✅ Database connected successfully
2025-07-07 13:48:50 - __main__ - INFO - initialize_async_components:68 - ✅ Services initialized
2025-07-07 13:48:50 - __main__ - INFO - initialize_async_components:73 - ✅ Default products initialized
2025-07-07 13:48:50 - __main__ - INFO - init_and_run:2889 - 🚀 Starting bot in long polling mode...
2025-07-07 13:48:50 - __main__ - INFO - init_and_run:2890 - ✅ Bot is running! Press Ctrl+C to stop.
2025-07-07 13:48:50 - __main__ - INFO - init_and_run:2891 - ℹ️ No webhook, domain, or SSL configuration required.
2025-07-07 13:48:50 - __main__ - INFO - init_and_run:2892 - 🤖 Bot username: @pro_gifts_bot
2025-07-07 13:56:33 - __main__ - INFO - main:3063 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-07 13:56:33 - __main__ - INFO - main:3064 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-07 13:56:33 - __main__ - INFO - main:3068 - ✅ Configuration validated
2025-07-07 13:56:33 - __main__ - INFO - main:3091 - ✅ All handlers added successfully
2025-07-07 13:56:33 - __main__ - INFO - initialize_async_components:45 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-07 13:56:33 - __main__ - INFO - initialize_async_components:46 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-07 13:56:33 - __main__ - INFO - initialize_async_components:50 - ✅ Configuration validated
2025-07-07 13:56:41 - __main__ - INFO - initialize_async_components:55 - ✅ Database connected successfully
2025-07-07 13:56:41 - __main__ - INFO - initialize_async_components:68 - ✅ Services initialized
2025-07-07 13:56:41 - __main__ - INFO - initialize_async_components:73 - ✅ Default products initialized
2025-07-07 13:56:41 - __main__ - INFO - init_and_run:3096 - 🚀 Starting bot in long polling mode...
2025-07-07 13:56:41 - __main__ - INFO - init_and_run:3097 - ✅ Bot is running! Press Ctrl+C to stop.
2025-07-07 13:56:41 - __main__ - INFO - init_and_run:3098 - ℹ️ No webhook, domain, or SSL configuration required.
2025-07-07 13:56:41 - __main__ - INFO - init_and_run:3099 - 🤖 Bot username: @pro_gifts_bot
2025-07-07 14:04:26 - __main__ - INFO - main:3063 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-07 14:04:26 - __main__ - INFO - main:3064 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-07 14:04:26 - __main__ - INFO - main:3068 - ✅ Configuration validated
2025-07-07 14:04:26 - __main__ - INFO - main:3091 - ✅ All handlers added successfully
2025-07-07 14:04:26 - __main__ - INFO - initialize_async_components:45 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-07 14:04:26 - __main__ - INFO - initialize_async_components:46 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-07 14:04:26 - __main__ - INFO - initialize_async_components:50 - ✅ Configuration validated
2025-07-07 14:04:33 - __main__ - INFO - initialize_async_components:55 - ✅ Database connected successfully
2025-07-07 14:04:33 - __main__ - INFO - initialize_async_components:68 - ✅ Services initialized
2025-07-07 14:04:33 - __main__ - INFO - initialize_async_components:73 - ✅ Default products initialized
2025-07-07 14:04:33 - __main__ - INFO - init_and_run:3096 - 🚀 Starting bot in long polling mode...
2025-07-07 14:04:33 - __main__ - INFO - init_and_run:3097 - ✅ Bot is running! Press Ctrl+C to stop.
2025-07-07 14:04:33 - __main__ - INFO - init_and_run:3098 - ℹ️ No webhook, domain, or SSL configuration required.
2025-07-07 14:04:33 - __main__ - INFO - init_and_run:3099 - 🤖 Bot username: @pro_gifts_bot
2025-07-07 14:07:27 - final_bot - ERROR - _show_task_management_menu:2138 - Error showing task management menu: object Mock can't be used in 'await' expression
2025-07-07 14:07:27 - final_bot - ERROR - _handle_admin_callbacks:1321 - Error in admin callback: object Mock can't be used in 'await' expression
2025-07-07 14:07:27 - final_bot - ERROR - _show_broadcast_menu:2198 - Error showing broadcast menu: object Mock can't be used in 'await' expression
2025-07-07 14:07:27 - final_bot - ERROR - _handle_admin_callbacks:1321 - Error in admin callback: object Mock can't be used in 'await' expression
2025-07-07 14:07:27 - final_bot - ERROR - _show_analytics_menu:1957 - Error showing analytics menu: object Mock can't be used in 'await' expression
2025-07-07 14:07:27 - final_bot - ERROR - _handle_admin_callbacks:1321 - Error in admin callback: object Mock can't be used in 'await' expression
2025-07-07 14:07:27 - final_bot - ERROR - _show_system_health:2011 - Error showing system health: object Mock can't be used in 'await' expression
2025-07-07 14:07:27 - final_bot - ERROR - _handle_admin_callbacks:1321 - Error in admin callback: object Mock can't be used in 'await' expression
2025-07-07 14:07:27 - final_bot - ERROR - _show_admin_logs:2087 - Error showing admin logs: object Mock can't be used in 'await' expression
2025-07-07 14:07:27 - final_bot - ERROR - _handle_admin_callbacks:1321 - Error in admin callback: object Mock can't be used in 'await' expression
2025-07-07 14:07:27 - final_bot - ERROR - _show_analytics_menu:1957 - Error showing analytics menu: object Mock can't be used in 'await' expression
2025-07-07 14:08:13 - final_bot - ERROR - _show_broadcast_menu:2198 - Error showing broadcast menu: object Mock can't be used in 'await' expression
2025-07-07 14:15:44 - __main__ - INFO - main:3386 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-07 14:15:44 - __main__ - INFO - main:3387 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-07 14:15:44 - __main__ - INFO - main:3391 - ✅ Configuration validated
2025-07-07 14:15:45 - __main__ - INFO - main:3414 - ✅ All handlers added successfully
2025-07-07 14:15:45 - __main__ - INFO - initialize_async_components:45 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-07 14:15:45 - __main__ - INFO - initialize_async_components:46 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-07 14:15:45 - __main__ - INFO - initialize_async_components:50 - ✅ Configuration validated
2025-07-07 14:15:52 - __main__ - INFO - initialize_async_components:55 - ✅ Database connected successfully
2025-07-07 14:15:52 - __main__ - INFO - initialize_async_components:68 - ✅ Services initialized
2025-07-07 14:15:52 - __main__ - INFO - initialize_async_components:73 - ✅ Default products initialized
2025-07-07 14:15:52 - __main__ - INFO - init_and_run:3419 - 🚀 Starting bot in long polling mode...
2025-07-07 14:15:52 - __main__ - INFO - init_and_run:3420 - ✅ Bot is running! Press Ctrl+C to stop.
2025-07-07 14:15:52 - __main__ - INFO - init_and_run:3421 - ℹ️ No webhook, domain, or SSL configuration required.
2025-07-07 14:15:52 - __main__ - INFO - init_and_run:3422 - 🤖 Bot username: @pro_gifts_bot
2025-07-09 12:45:10 - __main__ - INFO - main:3386 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-09 12:45:10 - __main__ - INFO - main:3387 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-09 12:45:10 - __main__ - INFO - main:3391 - ✅ Configuration validated
2025-07-09 12:45:10 - __main__ - INFO - main:3414 - ✅ All handlers added successfully
2025-07-09 12:45:10 - __main__ - INFO - initialize_async_components:45 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-09 12:45:10 - __main__ - INFO - initialize_async_components:46 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-09 12:45:10 - __main__ - INFO - initialize_async_components:50 - ✅ Configuration validated
2025-07-09 12:45:21 - __main__ - INFO - initialize_async_components:55 - ✅ Database connected successfully
2025-07-09 12:45:21 - __main__ - INFO - initialize_async_components:68 - ✅ Services initialized
2025-07-09 12:45:21 - __main__ - INFO - initialize_async_components:73 - ✅ Default products initialized
2025-07-09 12:45:21 - __main__ - INFO - init_and_run:3419 - 🚀 Starting bot in long polling mode...
2025-07-09 12:45:21 - __main__ - INFO - init_and_run:3420 - ✅ Bot is running! Press Ctrl+C to stop.
2025-07-09 12:45:21 - __main__ - INFO - init_and_run:3421 - ℹ️ No webhook, domain, or SSL configuration required.
2025-07-09 12:45:21 - __main__ - INFO - init_and_run:3422 - 🤖 Bot username: @pro_gifts_bot
2025-07-09 12:46:26 - __main__ - INFO - main:3386 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-09 12:46:26 - __main__ - INFO - main:3387 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-09 12:46:26 - __main__ - INFO - main:3391 - ✅ Configuration validated
2025-07-09 12:46:27 - __main__ - INFO - main:3414 - ✅ All handlers added successfully
2025-07-09 12:46:27 - __main__ - INFO - initialize_async_components:45 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-09 12:46:27 - __main__ - INFO - initialize_async_components:46 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-09 12:46:27 - __main__ - INFO - initialize_async_components:50 - ✅ Configuration validated
2025-07-09 12:46:38 - __main__ - INFO - initialize_async_components:55 - ✅ Database connected successfully
2025-07-09 12:46:38 - __main__ - INFO - initialize_async_components:68 - ✅ Services initialized
2025-07-09 12:46:39 - __main__ - INFO - initialize_async_components:73 - ✅ Default products initialized
2025-07-09 12:46:39 - __main__ - INFO - init_and_run:3419 - 🚀 Starting bot in long polling mode...
2025-07-09 12:46:39 - __main__ - INFO - init_and_run:3420 - ✅ Bot is running! Press Ctrl+C to stop.
2025-07-09 12:46:39 - __main__ - INFO - init_and_run:3421 - ℹ️ No webhook, domain, or SSL configuration required.
2025-07-09 12:46:39 - __main__ - INFO - init_and_run:3422 - 🤖 Bot username: @pro_gifts_bot
2025-07-09 13:12:28 - __main__ - INFO - main:3386 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-09 13:12:28 - __main__ - INFO - main:3387 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-09 13:12:28 - __main__ - INFO - main:3391 - ✅ Configuration validated
2025-07-09 13:12:29 - __main__ - INFO - main:3414 - ✅ All handlers added successfully
2025-07-09 13:12:29 - __main__ - INFO - initialize_async_components:45 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-09 13:12:29 - __main__ - INFO - initialize_async_components:46 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-09 13:12:29 - __main__ - INFO - initialize_async_components:50 - ✅ Configuration validated
2025-07-09 13:12:40 - __main__ - INFO - initialize_async_components:55 - ✅ Database connected successfully
2025-07-09 13:12:40 - __main__ - INFO - initialize_async_components:68 - ✅ Services initialized
2025-07-09 13:12:41 - __main__ - INFO - initialize_async_components:73 - ✅ Default products initialized
2025-07-09 13:12:41 - __main__ - INFO - init_and_run:3419 - 🚀 Starting bot in long polling mode...
2025-07-09 13:12:41 - __main__ - INFO - init_and_run:3420 - ✅ Bot is running! Press Ctrl+C to stop.
2025-07-09 13:12:41 - __main__ - INFO - init_and_run:3421 - ℹ️ No webhook, domain, or SSL configuration required.
2025-07-09 13:12:41 - __main__ - INFO - init_and_run:3422 - 🤖 Bot username: @pro_gifts_bot
2025-07-09 13:23:48 - __main__ - ERROR - _show_task_list:2723 - Error showing task list: 'CallbackQuery' object has no attribute 'callback_query'
2025-07-09 13:23:48 - __main__ - ERROR - _handle_admin_callbacks:1450 - Error in admin callback: 'CallbackQuery' object has no attribute 'callback_query'
2025-07-09 13:23:51 - __main__ - ERROR - _show_task_list:2723 - Error showing task list: 'CallbackQuery' object has no attribute 'callback_query'
2025-07-09 13:23:51 - __main__ - ERROR - _handle_admin_callbacks:1450 - Error in admin callback: 'CallbackQuery' object has no attribute 'callback_query'
2025-07-09 13:42:38 - __main__ - INFO - main:371 - 🤖 Starting Telegram Referral Bot...
2025-07-09 13:42:38 - __main__ - INFO - main:375 - ✅ Configuration validated
2025-07-09 13:42:39 - __main__ - ERROR - main:414 - ❌ Bot error: 'NoneType' object has no attribute 'admin_panel_command'
2025-07-09 14:01:51 - __main__ - INFO - main:389 - 🤖 Starting Telegram Referral Bot...
2025-07-09 14:01:51 - __main__ - INFO - main:393 - ✅ Configuration validated
2025-07-09 14:01:52 - __main__ - ERROR - main:432 - ❌ Bot error: 'NoneType' object has no attribute 'admin_panel_command'
2025-07-09 14:02:39 - __main__ - INFO - main:389 - 🤖 Starting Telegram Referral Bot...
2025-07-09 14:02:39 - __main__ - INFO - main:393 - ✅ Configuration validated
2025-07-09 14:02:40 - __main__ - INFO - initialize_async_components:50 - 🤖 Starting Telegram Referral Bot...
2025-07-09 14:02:40 - __main__ - INFO - initialize_async_components:51 - 🔄 Mode: Long Polling
2025-07-09 14:02:40 - __main__ - INFO - initialize_async_components:55 - ✅ Configuration validated
2025-07-09 14:02:53 - __main__ - INFO - initialize_async_components:60 - ✅ Database connected
2025-07-09 14:02:54 - __main__ - INFO - initialize_async_components:74 - ✅ Services initialized
2025-07-09 14:02:54 - __main__ - INFO - initialize_async_components:88 - ✅ Default products initialized
2025-07-09 14:02:54 - __main__ - INFO - init_and_run:416 - ✅ All handlers added
2025-07-09 14:02:54 - __main__ - INFO - init_and_run:417 - 🚀 Bot is running! Press Ctrl+C to stop.
2025-07-09 14:03:19 - __main__ - INFO - main:389 - 🤖 Starting Telegram Referral Bot...
2025-07-09 14:03:19 - __main__ - INFO - main:393 - ✅ Configuration validated
2025-07-09 14:03:19 - __main__ - INFO - initialize_async_components:50 - 🤖 Starting Telegram Referral Bot...
2025-07-09 14:03:19 - __main__ - INFO - initialize_async_components:51 - 🔄 Mode: Long Polling
2025-07-09 14:03:19 - __main__ - INFO - initialize_async_components:55 - ✅ Configuration validated
2025-07-09 14:03:32 - __main__ - INFO - initialize_async_components:60 - ✅ Database connected
2025-07-09 14:03:32 - __main__ - INFO - initialize_async_components:74 - ✅ Services initialized
2025-07-09 14:03:33 - __main__ - INFO - initialize_async_components:88 - ✅ Default products initialized
2025-07-09 14:03:33 - __main__ - INFO - init_and_run:416 - ✅ All handlers added
2025-07-09 14:03:33 - __main__ - INFO - init_and_run:417 - 🚀 Bot is running! Press Ctrl+C to stop.
2025-07-09 14:03:34 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:03:38 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:03:39 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:03:44 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:03:46 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:03:51 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:03:54 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:03:57 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:04:01 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:04:06 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:04:12 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:04:27 - __main__ - ERROR - check_user_permissions:126 - Error checking permissions: 'BanManager' object has no attribute 'is_banned'
2025-07-09 14:04:47 - __main__ - ERROR - check_user_permissions:126 - Error checking permissions: 'BanManager' object has no attribute 'is_banned'
2025-07-09 14:04:47 - __main__ - ERROR - check_user_permissions:126 - Error checking permissions: 'BanManager' object has no attribute 'is_banned'
2025-07-09 14:04:47 - __main__ - ERROR - balance_command:323 - Error in balance command: 'TransactionService' object has no attribute 'get_today_earnings'
2025-07-09 14:05:20 - __main__ - ERROR - check_user_permissions:126 - Error checking permissions: 'BanManager' object has no attribute 'is_banned'
2025-07-09 14:05:23 - __main__ - ERROR - check_user_permissions:126 - Error checking permissions: 'BanManager' object has no attribute 'is_banned'
2025-07-09 14:05:24 - __main__ - ERROR - check_user_permissions:126 - Error checking permissions: 'BanManager' object has no attribute 'is_banned'
2025-07-09 14:05:24 - __main__ - ERROR - check_user_permissions:126 - Error checking permissions: 'BanManager' object has no attribute 'is_banned'
2025-07-09 14:05:24 - __main__ - ERROR - help_command:287 - Error in help command: type object 'Config' has no attribute 'SUPPORT_USERNAME'
2025-07-09 14:05:25 - __main__ - ERROR - check_user_permissions:126 - Error checking permissions: 'BanManager' object has no attribute 'is_banned'
2025-07-09 14:05:25 - __main__ - ERROR - check_user_permissions:126 - Error checking permissions: 'BanManager' object has no attribute 'is_banned'
2025-07-09 14:06:09 - __main__ - INFO - main:382 - 🤖 Starting Telegram Referral Bot...
2025-07-09 14:06:09 - __main__ - INFO - main:386 - ✅ Configuration validated
2025-07-09 14:06:09 - __main__ - INFO - initialize_async_components:50 - 🤖 Starting Telegram Referral Bot...
2025-07-09 14:06:09 - __main__ - INFO - initialize_async_components:51 - 🔄 Mode: Long Polling
2025-07-09 14:06:09 - __main__ - INFO - initialize_async_components:55 - ✅ Configuration validated
2025-07-09 14:06:22 - __main__ - INFO - initialize_async_components:60 - ✅ Database connected
2025-07-09 14:06:23 - __main__ - INFO - initialize_async_components:74 - ✅ Services initialized
2025-07-09 14:06:23 - __main__ - INFO - initialize_async_components:88 - ✅ Default products initialized
2025-07-09 14:06:23 - __main__ - INFO - init_and_run:409 - ✅ All handlers added
2025-07-09 14:06:23 - __main__ - INFO - init_and_run:410 - 🚀 Bot is running! Press Ctrl+C to stop.
2025-07-09 14:06:24 - __main__ - ERROR - error_handler:383 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 14:06:29 - __main__ - ERROR - error_handler:376 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
