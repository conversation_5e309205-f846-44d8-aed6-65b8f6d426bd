"""
Admin command handlers for the Telegram bot
"""

from telegram import Update, InlineKeyboardMarkup, InlineKeyboardButton
from telegram.ext import ContextTypes

from config import Config
from src.models.task import TaskType
from src.utils.logger import setup_logger, log_admin_action

logger = setup_logger(__name__)


class AdminHandlers:
    """Handles admin-related commands and interactions"""
    
    def __init__(self, bot_instance):
        self.bot = bot_instance
        self.services = bot_instance.services
    
    async def admin_panel_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Main admin panel entry point"""
        try:
            user_id = update.effective_user.id

            # Check admin access
            if not await self.services['admin_panel'].validate_admin_access(user_id, Config.ADMIN_USER_IDS):
                await update.message.reply_text("❌ Access denied. Admin privileges required.")
                return

            # Show main admin menu
            await self.show_admin_main_menu(update, context)

        except Exception as e:
            logger.error(f"Error in admin panel command: {e}")
            await update.message.reply_text("❌ Failed to access admin panel.")
    
    async def show_admin_main_menu(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Show main admin panel menu"""
        try:
            admin_text = """
🔧 **ADMIN CONTROL PANEL** 🔧

*Welcome to the bot administration center.*

*Select a section below to get started:*
            """

            keyboard = [
                [InlineKeyboardButton("👥 User Management", callback_data="admin_users"),
                 InlineKeyboardButton("⚙️ Bot Settings", callback_data="admin_settings_menu")],
                [InlineKeyboardButton("📋 Task Management", callback_data="admin_tasks"),
                 InlineKeyboardButton("📢 Broadcasting", callback_data="admin_broadcast")],
                [InlineKeyboardButton("📊 Analytics", callback_data="admin_analytics"),
                 InlineKeyboardButton("🔒 System Health", callback_data="admin_health")],
                [InlineKeyboardButton("📋 Admin Logs", callback_data="admin_logs")]
            ]

            reply_markup = InlineKeyboardMarkup(keyboard)

            await update.message.reply_text(
                admin_text,
                parse_mode='Markdown',
                reply_markup=reply_markup
            )

        except Exception as e:
            logger.error(f"Error showing admin main menu: {e}")
            await update.message.reply_text("❌ Failed to load admin menu.")
    
    async def show_admin_main_menu_callback(self, query, context):
        """Show main admin panel menu for callback queries"""
        try:
            admin_text = """
🔧 **ADMIN CONTROL PANEL** 🔧

*Welcome to the bot administration center.*

*Select a section below to get started:*
            """

            keyboard = [
                [InlineKeyboardButton("👥 User Management", callback_data="admin_users"),
                 InlineKeyboardButton("⚙️ Bot Settings", callback_data="admin_settings_menu")],
                [InlineKeyboardButton("📋 Task Management", callback_data="admin_tasks"),
                 InlineKeyboardButton("📢 Broadcasting", callback_data="admin_broadcast")],
                [InlineKeyboardButton("📊 Analytics", callback_data="admin_analytics"),
                 InlineKeyboardButton("🔒 System Health", callback_data="admin_health")],
                [InlineKeyboardButton("📋 Admin Logs", callback_data="admin_logs")]
            ]

            reply_markup = InlineKeyboardMarkup(keyboard)

            await query.edit_message_text(
                admin_text,
                parse_mode='Markdown',
                reply_markup=reply_markup
            )

        except Exception as e:
            logger.error(f"Error showing admin main menu callback: {e}")
            await query.answer("❌ Failed to load admin menu.")
    
    async def show_user_management_menu(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Show user management menu"""
        try:
            # Get basic user statistics
            stats = await self.services['admin_panel'].get_user_statistics()
            
            menu_text = f"""
👥 **USER MANAGEMENT** 👥

• **Total Users:** {stats.get('total_users', 0)}
• **Active Users (7 days):** {stats.get('active_7_days', 0)}
• **New Users (24h):** {stats.get('new_24h', 0)}

*Select an option below:*
            """

            keyboard = [
                [InlineKeyboardButton("📋 View Users List", callback_data="admin_users_list"),
                 InlineKeyboardButton("📊 User Reports", callback_data="admin_user_reports")],
                [InlineKeyboardButton("🔍 Search User", callback_data="admin_search_user"),
                 InlineKeyboardButton("💰 Manage Balances", callback_data="admin_manage_balances")],
                [InlineKeyboardButton("🚫 Ban/Unban Users", callback_data="admin_ban_users"),
                 InlineKeyboardButton("📢 Message User", callback_data="admin_message_user")],
                [InlineKeyboardButton("🔙 Back to Main Menu", callback_data="admin_main")]
            ]

            reply_markup = InlineKeyboardMarkup(keyboard)

            await update.callback_query.edit_message_text(
                menu_text,
                parse_mode='Markdown',
                reply_markup=reply_markup
            )

        except Exception as e:
            logger.error(f"Error showing user management menu: {e}")
            await update.callback_query.answer("❌ Failed to load user management menu.")
    
    async def show_user_management_menu_callback(self, query, context):
        """Show user management menu for callback queries"""
        try:
            # Get basic user statistics
            stats = await self.services['admin_panel'].get_user_statistics()
            
            menu_text = f"""
👥 **USER MANAGEMENT** 👥

• **Total Users:** {stats.get('total_users', 0)}
• **Active Users (7 days):** {stats.get('active_7_days', 0)}
• **New Users (24h):** {stats.get('new_24h', 0)}

*Select an option below:*
            """

            keyboard = [
                [InlineKeyboardButton("📋 View Users List", callback_data="admin_users_list"),
                 InlineKeyboardButton("📊 User Reports", callback_data="admin_user_reports")],
                [InlineKeyboardButton("🔍 Search User", callback_data="admin_search_user"),
                 InlineKeyboardButton("💰 Manage Balances", callback_data="admin_manage_balances")],
                [InlineKeyboardButton("🚫 Ban/Unban Users", callback_data="admin_ban_users"),
                 InlineKeyboardButton("📢 Message User", callback_data="admin_message_user")],
                [InlineKeyboardButton("🔙 Back to Main Menu", callback_data="admin_main")]
            ]

            reply_markup = InlineKeyboardMarkup(keyboard)

            await query.edit_message_text(
                menu_text,
                parse_mode='Markdown',
                reply_markup=reply_markup
            )

        except Exception as e:
            logger.error(f"Error showing user management menu callback: {e}")
            await query.answer("❌ Failed to load user management menu.")
    
    async def show_bot_settings_menu(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Show bot settings management menu"""
        try:
            # Get current settings
            settings = await self.services['admin_settings'].get_all_settings()
            
            settings_text = f"""
⚙️ **BOT SETTINGS** ⚙️

**Current Configuration:**

• **Referral Reward:** 💎{settings.referral_reward}
• **Daily Bonus:** 💎{settings.daily_bonus_amount}
• **Min Withdrawal:** 💎{settings.minimum_withdrawal}

• **Welcome Bonus:** 💎{settings.welcome_bonus}

*Select a setting to modify:*
            """

            keyboard = [
                [InlineKeyboardButton("💰 Referral Reward", callback_data="admin_set_referral"),
                 InlineKeyboardButton("🎁 Daily Bonus", callback_data="admin_set_daily")],
                [InlineKeyboardButton("💸 Min Withdrawal", callback_data="admin_set_withdrawal"),
                 InlineKeyboardButton("⏰ Withdrawal Cooldown", callback_data="admin_set_cooldown")],
                [InlineKeyboardButton("🎊 Welcome Bonus", callback_data="admin_set_welcome"),
                 InlineKeyboardButton("🔢 Daily Limits", callback_data="admin_set_limits")],
                [InlineKeyboardButton("🔄 Reset to Defaults", callback_data="admin_reset_settings"),
                 InlineKeyboardButton("💾 Export Settings", callback_data="admin_export_settings")],
                [InlineKeyboardButton("🔙 Back to Main Menu", callback_data="admin_main")]
            ]

            reply_markup = InlineKeyboardMarkup(keyboard)

            await update.callback_query.edit_message_text(
                settings_text,
                parse_mode='Markdown',
                reply_markup=reply_markup
            )

        except Exception as e:
            logger.error(f"Error showing bot settings menu: {e}")
            await update.callback_query.answer("❌ Failed to load settings menu.")
    
    async def show_bot_settings_menu_callback(self, query, context):
        """Show bot settings management menu for callback queries"""
        try:
            # Get current settings
            settings = await self.services['admin_settings'].get_all_settings()
            
            settings_text = f"""
⚙️ **BOT SETTINGS** ⚙️

**Current Configuration:**

• **Referral Reward:** 💎{settings.referral_reward}
• **Daily Bonus:** 💎{settings.daily_bonus_amount}
• **Min Withdrawal:** 💎{settings.minimum_withdrawal}

• **Welcome Bonus:** 💎{settings.welcome_bonus}

*Select a setting to modify:*
            """

            keyboard = [
                [InlineKeyboardButton("💰 Referral Reward", callback_data="admin_set_referral"),
                 InlineKeyboardButton("🎁 Daily Bonus", callback_data="admin_set_daily")],
                [InlineKeyboardButton("💸 Min Withdrawal", callback_data="admin_set_withdrawal"),
                 InlineKeyboardButton("⏰ Withdrawal Cooldown", callback_data="admin_set_cooldown")],
                [InlineKeyboardButton("🎊 Welcome Bonus", callback_data="admin_set_welcome"),
                 InlineKeyboardButton("🔢 Daily Limits", callback_data="admin_set_limits")],
                [InlineKeyboardButton("🔄 Reset to Defaults", callback_data="admin_reset_settings"),
                 InlineKeyboardButton("💾 Export Settings", callback_data="admin_export_settings")],
                [InlineKeyboardButton("🔙 Back to Main Menu", callback_data="admin_main")]
            ]

            reply_markup = InlineKeyboardMarkup(keyboard)

            await query.edit_message_text(
                settings_text,
                parse_mode='Markdown',
                reply_markup=reply_markup
            )

        except Exception as e:
            logger.error(f"Error showing bot settings menu callback: {e}")
            await query.answer("❌ Failed to load settings menu.")

    # ==================== TASK MANAGEMENT ====================

    async def show_task_management_menu(self, query, context):
        """Show task management menu"""
        try:
            # Get task statistics
            task_service = self.services.get('task_management')
            if not task_service:
                await query.edit_message_text("❌ Task management service not available.")
                return

            stats = await task_service.get_task_statistics()

            menu_text = f"""
📋 **TASK MANAGEMENT** 📋

**Current Statistics:**
• **Active Tasks:** {stats.get('total_active_tasks', 0)}
• **Total Completions:** {stats.get('total_completions', 0)}
• **Rewards Distributed:** 💎{stats.get('total_rewards_distributed', 0):.2f}
• **Pending Reviews:** {stats.get('pending_submissions', 0)}

*Select an option below:*
            """

            keyboard = [
                [InlineKeyboardButton("➕ Create New Task", callback_data="admin_task_create"),
                 InlineKeyboardButton("📋 View All Tasks", callback_data="admin_task_list")],
                [InlineKeyboardButton("⏳ Pending Reviews", callback_data="admin_task_reviews"),
                 InlineKeyboardButton("📊 Task Statistics", callback_data="admin_task_stats")],
                [InlineKeyboardButton("🔙 Back to Main Menu", callback_data="admin_main")]
            ]

            reply_markup = InlineKeyboardMarkup(keyboard)

            await query.edit_message_text(
                menu_text,
                parse_mode='Markdown',
                reply_markup=reply_markup
            )

        except Exception as e:
            logger.error(f"Error showing task management menu: {e}")
            await query.answer("❌ Failed to load task management menu.")

    async def initiate_task_creation(self, query, context):
        """Start task creation process"""
        try:
            creation_text = """
➕ **CREATE NEW TASK**

**Step 1: Select Task Type**

Choose the type of task you want to create:
            """

            keyboard = [
                [InlineKeyboardButton("📺 Join Channel", callback_data="admin_task_type_join_channel")],
                [InlineKeyboardButton("📸 Submit Image", callback_data="admin_task_type_submit_image")],
                [InlineKeyboardButton("🔙 Back to Task Menu", callback_data="admin_tasks")]
            ]

            reply_markup = InlineKeyboardMarkup(keyboard)

            await query.edit_message_text(
                creation_text,
                parse_mode='Markdown',
                reply_markup=reply_markup
            )

        except Exception as e:
            logger.error(f"Error initiating task creation: {e}")
            await query.answer("❌ Failed to start task creation.")

    async def start_join_channel_task_creation(self, query, context):
        """Start join channel task creation"""
        try:
            # Store task creation state
            context.user_data['admin_task_creation'] = {
                'step': 'task_name',
                'task_type': 'join_channel',
                'data': {}
            }

            instruction_text = """
📺 **CREATE JOIN CHANNEL TASK**

**Step 1: Task Button Name**

Enter the display name that users will see for this task.

*Example: "Join Our Main Channel", "Subscribe to Updates"*

Please send the task button name:
            """

            await query.edit_message_text(
                instruction_text,
                parse_mode='Markdown'
            )

        except Exception as e:
            logger.error(f"Error starting join channel task creation: {e}")
            await query.answer("❌ Failed to start task creation.")

    async def start_image_submission_task_creation(self, query, context):
        """Start image submission task creation"""
        try:
            # Store task creation state
            context.user_data['admin_task_creation'] = {
                'step': 'task_name',
                'task_type': 'submit_image',
                'data': {}
            }

            instruction_text = """
📸 **CREATE IMAGE SUBMISSION TASK**

**Step 1: Task Button Name**

Enter the display name that users will see for this task.

*Example: "Submit Screenshot", "Upload Proof"*

Please send the task button name:
            """

            await query.edit_message_text(
                instruction_text,
                parse_mode='Markdown'
            )

        except Exception as e:
            logger.error(f"Error starting image submission task creation: {e}")
            await query.answer("❌ Failed to start task creation.")

    async def process_task_creation_step(self, update, context, message_text):
        """Process task creation steps"""
        try:
            if 'admin_task_creation' not in context.user_data:
                await update.message.reply_text("❌ No active task creation session.")
                return

            creation_data = context.user_data['admin_task_creation']
            step = creation_data['step']
            task_type = creation_data['task_type']
            data = creation_data['data']

            if step == 'task_name':
                # Store task name and move to next step
                data['task_name'] = message_text.strip()

                if task_type == 'join_channel':
                    creation_data['step'] = 'channel_id'
                    await update.message.reply_text(
                        "✅ Task name set!\n\n"
                        "**Step 2: Channel ID**\n\n"
                        "Enter the channel ID or username.\n"
                        "*Format: @channelname or -100xxxxxxxxx*",
                        parse_mode='Markdown'
                    )
                elif task_type == 'submit_image':
                    creation_data['step'] = 'task_caption'
                    await update.message.reply_text(
                        "✅ Task name set!\n\n"
                        "**Step 2: Task Instructions**\n\n"
                        "Enter detailed instructions for users about what they need to do.\n"
                        "*Example: Take a screenshot of your profile page showing...*",
                        parse_mode='Markdown'
                    )

            elif step == 'channel_id' and task_type == 'join_channel':
                # Validate and store channel ID
                channel_id = message_text.strip()
                if not (channel_id.startswith('@') or channel_id.startswith('-100')):
                    await update.message.reply_text(
                        "❌ Invalid channel ID format.\n"
                        "Please use @channelname or -100xxxxxxxxx format."
                    )
                    return

                data['channel_id'] = channel_id
                creation_data['step'] = 'join_link'
                await update.message.reply_text(
                    "✅ Channel ID set!\n\n"
                    "**Step 3: Join Link**\n\n"
                    "Enter the public join link for the channel.\n"
                    "*Example: https://t.me/yourchannel*",
                    parse_mode='Markdown'
                )

            elif step == 'join_link' and task_type == 'join_channel':
                # Store join link and move to reward step
                join_link = message_text.strip()
                if not join_link.startswith('https://t.me/'):
                    await update.message.reply_text(
                        "❌ Invalid join link format.\n"
                        "Please use https://t.me/channelname format."
                    )
                    return

                data['join_link'] = join_link
                creation_data['step'] = 'reward_amount'
                await update.message.reply_text(
                    "✅ Join link set!\n\n"
                    "**Step 4: Reward Amount**\n\n"
                    "Enter the reward amount in diamonds (💎1-💎500):",
                    parse_mode='Markdown'
                )

            elif step == 'task_caption' and task_type == 'submit_image':
                # Store caption and move to reward step
                data['caption'] = message_text.strip()
                creation_data['step'] = 'reward_amount'
                await update.message.reply_text(
                    "✅ Instructions set!\n\n"
                    "**Step 3: Reward Amount**\n\n"
                    "Enter the reward amount in diamonds (💎1-💎500):",
                    parse_mode='Markdown'
                )

            elif step == 'reward_amount':
                # Validate and store reward amount
                try:
                    reward = float(message_text.strip())
                    if reward < 1 or reward > 500:
                        await update.message.reply_text(
                            "❌ Reward amount must be between 💎1 and 💎500."
                        )
                        return

                    data['reward_amount'] = reward

                    # Show confirmation and create task
                    await self.confirm_and_create_task(update, context, creation_data)

                except ValueError:
                    await update.message.reply_text(
                        "❌ Invalid reward amount. Please enter a number between 1 and 500."
                    )

        except Exception as e:
            logger.error(f"Error processing task creation step: {e}")
            await update.message.reply_text("❌ Failed to process task creation step.")

    async def confirm_and_create_task(self, update, context, creation_data):
        """Show confirmation and create the task"""
        try:
            task_type = creation_data['task_type']
            data = creation_data['data']

            # Prepare confirmation message
            if task_type == 'join_channel':
                confirmation_text = f"""
📺 **TASK CREATION CONFIRMATION**

• **Task Name:** {data['task_name']}
• **Type:** Join Channel
• **Channel ID:** {data['channel_id']}
• **Join Link:** {data['join_link']}
• **Reward:** 💎{data['reward_amount']}

**Confirm creation?**
                """
            else:  # submit_image
                confirmation_text = f"""
📸 **TASK CREATION CONFIRMATION**

• **Task Name:** {data['task_name']}
• **Type:** Submit Image
• **Instructions:** {data['caption'][:100]}...
• **Reward:** 💎{data['reward_amount']}

**Confirm creation?**
                """

            keyboard = [
                [InlineKeyboardButton("✅ Create Task", callback_data="admin_task_confirm_create")],
                [InlineKeyboardButton("❌ Cancel", callback_data="admin_tasks")]
            ]

            reply_markup = InlineKeyboardMarkup(keyboard)

            await update.message.reply_text(
                confirmation_text,
                parse_mode='Markdown',
                reply_markup=reply_markup
            )

        except Exception as e:
            logger.error(f"Error showing task confirmation: {e}")
            await update.message.reply_text("❌ Failed to show task confirmation.")

    async def create_task_confirmed(self, query, context):
        """Create the task after confirmation"""
        try:
            if 'admin_task_creation' not in context.user_data:
                await query.edit_message_text("❌ No active task creation session.")
                return

            creation_data = context.user_data['admin_task_creation']
            task_type = creation_data['task_type']
            data = creation_data['data']
            admin_id = query.from_user.id

            # Prepare task data
            task_data = {
                'task_name': data['task_name'],
                'task_type': TaskType.JOIN_CHANNEL if task_type == 'join_channel' else TaskType.SUBMIT_IMAGE,
                'reward_amount': data['reward_amount'],
                'is_active': True
            }

            if task_type == 'join_channel':
                task_data.update({
                    'channel_id': data['channel_id'],
                    'join_link': data['join_link']
                })
            else:  # submit_image
                task_data.update({
                    'caption': data['caption'],
                    'verification_mode': 'manual'
                })

            # Create the task
            task_service = self.services.get('task_management')
            if not task_service:
                await query.edit_message_text("❌ Task management service not available.")
                return

            success, message, task = await task_service.create_task(task_data, admin_id)

            if success:
                # Clear creation data
                del context.user_data['admin_task_creation']

                success_text = f"""
✅ **TASK CREATED SUCCESSFULLY!**

**Task ID:** {task.task_id}
**Name:** {task.task_name}
**Type:** {task.task_type.value.replace('_', ' ').title()}
**Reward:** 💎{task.reward_amount}

The task is now active and available to users.
                """

                keyboard = [
                    [InlineKeyboardButton("📋 View All Tasks", callback_data="admin_task_list")],
                    [InlineKeyboardButton("🔙 Back to Task Menu", callback_data="admin_tasks")]
                ]

                reply_markup = InlineKeyboardMarkup(keyboard)

                await query.edit_message_text(
                    success_text,
                    parse_mode='Markdown',
                    reply_markup=reply_markup
                )
            else:
                await query.edit_message_text(f"❌ Failed to create task: {message}")

        except Exception as e:
            logger.error(f"Error creating task: {e}")
            await query.edit_message_text("❌ Failed to create task.")

    async def show_task_list(self, query, context):
        """Show list of all tasks"""
        try:
            task_service = self.services.get('task_management')
            if not task_service:
                await query.edit_message_text("❌ Task management service not available.")
                return

            tasks = await task_service.get_all_tasks(include_inactive=True)

            if not tasks:
                list_text = """
📋 **ALL TASKS**

No tasks have been created yet.
                """
                keyboard = [
                    [InlineKeyboardButton("➕ Create New Task", callback_data="admin_task_create")],
                    [InlineKeyboardButton("🔙 Back to Task Menu", callback_data="admin_tasks")]
                ]
            else:
                list_text = f"""
📋 **ALL TASKS** ({len(tasks)} total)

"""
                for i, task in enumerate(tasks, 1):
                    status = "🟢 Active" if task.is_active else "🔴 Inactive"
                    task_type = task.task_type.value.replace('_', ' ').title()
                    list_text += f"{i}. **{task.task_name}**\n"
                    list_text += f"   Type: {task_type} | Reward: 💎{task.reward_amount} | {status}\n"
                    list_text += f"   Completions: {task.total_completions}\n\n"

                keyboard = [
                    [InlineKeyboardButton("➕ Create New Task", callback_data="admin_task_create")],
                    [InlineKeyboardButton("🔙 Back to Task Menu", callback_data="admin_tasks")]
                ]

            reply_markup = InlineKeyboardMarkup(keyboard)

            await query.edit_message_text(
                list_text,
                parse_mode='Markdown',
                reply_markup=reply_markup
            )

        except Exception as e:
            logger.error(f"Error showing task list: {e}")
            await query.answer("❌ Failed to load task list.")

    async def show_pending_reviews(self, query, context):
        """Show pending task submissions for review"""
        try:
            task_service = self.services.get('task_management')
            if not task_service:
                await query.edit_message_text("❌ Task management service not available.")
                return

            submissions = await task_service.get_pending_submissions()

            if not submissions:
                review_text = """
⏳ **PENDING REVIEWS**

No submissions pending review.
                """
                keyboard = [
                    [InlineKeyboardButton("🔄 Refresh", callback_data="admin_task_reviews")],
                    [InlineKeyboardButton("🔙 Back to Task Menu", callback_data="admin_tasks")]
                ]
            else:
                review_text = f"""
⏳ **PENDING REVIEWS** ({len(submissions)} submissions)

"""
                for i, sub in enumerate(submissions, 1):
                    review_text += f"{i}. **{sub['task_name']}**\n"
                    review_text += f"   User ID: {sub['user_id']}\n"
                    review_text += f"   Reward: 💎{sub['reward_amount']}\n"
                    review_text += f"   Submitted: {sub['submission_date'].strftime('%d/%m/%Y %H:%M')}\n\n"

                # Create buttons for each submission
                keyboard = []
                for sub in submissions[:5]:  # Show first 5 submissions
                    keyboard.append([
                        InlineKeyboardButton(
                            f"📋 Review: {sub['task_name'][:20]}...",
                            callback_data=f"admin_review_{sub['submission_id']}"
                        )
                    ])

                keyboard.extend([
                    [InlineKeyboardButton("🔄 Refresh", callback_data="admin_task_reviews")],
                    [InlineKeyboardButton("🔙 Back to Task Menu", callback_data="admin_tasks")]
                ])

            reply_markup = InlineKeyboardMarkup(keyboard)

            await query.edit_message_text(
                review_text,
                parse_mode='Markdown',
                reply_markup=reply_markup
            )

        except Exception as e:
            logger.error(f"Error showing pending reviews: {e}")
            await query.answer("❌ Failed to load pending reviews.")

    async def show_submission_review(self, query, context, submission_id):
        """Show individual submission for review"""
        try:
            task_service = self.services.get('task_management')
            if not task_service:
                await query.edit_message_text("❌ Task management service not available.")
                return

            # Get submission details
            submissions = await task_service.get_pending_submissions()
            submission = next((s for s in submissions if s['submission_id'] == submission_id), None)

            if not submission:
                await query.edit_message_text("❌ Submission not found or already reviewed.")
                return

            review_text = f"""
📋 **SUBMISSION REVIEW**

**Task:** {submission['task_name']}
**User ID:** {submission['user_id']}
**Reward:** 💎{submission['reward_amount']}
**Submitted:** {submission['submission_date'].strftime('%d/%m/%Y %H:%M')}

**User's Text:** {submission.get('submission_text', 'No text provided')}

*Image will be shown below this message.*
            """

            keyboard = [
                [InlineKeyboardButton("✅ Approve", callback_data=f"admin_approve_{submission_id}"),
                 InlineKeyboardButton("❌ Reject", callback_data=f"admin_reject_{submission_id}")],
                [InlineKeyboardButton("🔙 Back to Reviews", callback_data="admin_task_reviews")]
            ]

            reply_markup = InlineKeyboardMarkup(keyboard)

            # Send the review message
            await query.edit_message_text(
                review_text,
                parse_mode='Markdown',
                reply_markup=reply_markup
            )

            # If there's an image, send it separately
            if submission.get('submitted_image'):
                try:
                    # Note: In a real implementation, you'd need to handle image storage/retrieval
                    # This is a placeholder for the image display logic
                    await context.bot.send_message(
                        chat_id=query.message.chat_id,
                        text=f"📸 **Submitted Image for Review**\n\nSubmission ID: {submission_id}",
                        parse_mode='Markdown'
                    )
                except Exception as img_error:
                    logger.error(f"Error sending submission image: {img_error}")

        except Exception as e:
            logger.error(f"Error showing submission review: {e}")
            await query.answer("❌ Failed to load submission review.")

    async def approve_submission(self, query, context, submission_id):
        """Approve a task submission"""
        try:
            task_service = self.services.get('task_management')
            if not task_service:
                await query.edit_message_text("❌ Task management service not available.")
                return

            admin_id = query.from_user.id
            success, message, result_data = await task_service.review_submission(
                submission_id, True, admin_id, "Approved by admin"
            )

            if success:
                approval_text = f"""
✅ **SUBMISSION APPROVED**

**User ID:** {result_data['user_id']}
**Task:** {result_data['task_name']}
**Reward Added:** 💎{result_data['reward_amount']}

The user has been notified and the reward has been added to their balance.
                """

                # Notify the user
                try:
                    await context.bot.send_message(
                        chat_id=result_data['user_id'],
                        text=f"✅ **Task Approved!**\n\n"
                             f"Your submission for '{result_data['task_name']}' has been approved!\n"
                             f"💎{result_data['reward_amount']} has been added to your balance.",
                        parse_mode='Markdown'
                    )
                except Exception as notify_error:
                    logger.error(f"Error notifying user: {notify_error}")

            else:
                approval_text = f"❌ Failed to approve submission: {message}"

            keyboard = [
                [InlineKeyboardButton("🔙 Back to Reviews", callback_data="admin_task_reviews")]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)

            await query.edit_message_text(
                approval_text,
                parse_mode='Markdown',
                reply_markup=reply_markup
            )

        except Exception as e:
            logger.error(f"Error approving submission: {e}")
            await query.answer("❌ Failed to approve submission.")

    async def reject_submission(self, query, context, submission_id):
        """Reject a task submission"""
        try:
            task_service = self.services.get('task_management')
            if not task_service:
                await query.edit_message_text("❌ Task management service not available.")
                return

            admin_id = query.from_user.id
            success, message, result_data = await task_service.review_submission(
                submission_id, False, admin_id, "Submission does not meet requirements"
            )

            if success:
                rejection_text = f"""
❌ **SUBMISSION REJECTED**

**User ID:** {result_data['user_id']}
**Task:** {result_data['task_name']}

The user has been notified and can resubmit if they wish.
                """

                # Notify the user
                try:
                    await context.bot.send_message(
                        chat_id=result_data['user_id'],
                        text=f"❌ **Task Submission Rejected**\n\n"
                             f"Your submission for '{result_data['task_name']}' was rejected.\n"
                             f"Please ensure you follow the instructions correctly.\n\n"
                             f"⚠️ Repeated violations may result in permanent ban.",
                        parse_mode='Markdown'
                    )
                except Exception as notify_error:
                    logger.error(f"Error notifying user: {notify_error}")

            else:
                rejection_text = f"❌ Failed to reject submission: {message}"

            keyboard = [
                [InlineKeyboardButton("🔙 Back to Reviews", callback_data="admin_task_reviews")]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)

            await query.edit_message_text(
                rejection_text,
                parse_mode='Markdown',
                reply_markup=reply_markup
            )

        except Exception as e:
            logger.error(f"Error rejecting submission: {e}")
            await query.answer("❌ Failed to reject submission.")
