# Production Deployment Guide

## Overview
This guide covers deploying the Telegram Referral Bot to production environments.

## Prerequisites
- Python 3.8+
- MongoDB Atlas account
- Telegram Bo<PERSON> Token
- Server with internet access

## Quick Start

### 1. Environment Setup
```bash
# Clone the repository
git clone <repository-url>
cd refer-earn-bot

# Create virtual environment
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt
```

### 2. Configuration
```bash
# Copy environment template
cp .env.example .env

# Edit .env with your values
nano .env
```

Required environment variables:
- `BOT_TOKEN`: Your Telegram bot token from @BotFather
- `BOT_USERNAME`: Your bot username (without @)
- `MONGODB_URI`: MongoDB Atlas connection string
- `ADMIN_USER_IDS`: Comma-separated admin user IDs

### 3. Database Setup
The bot automatically creates required collections and indexes on first run.

### 4. Run the Bot
```bash
# Development
python bot.py

# Production with process manager
pm2 start bot.py --name telegram-bot --interpreter python3
```

## Production Considerations

### Security
- Use strong passwords and secret keys
- Restrict admin access to trusted users only
- Enable MongoDB Atlas IP whitelist
- Use HTTPS for webhook deployments (if needed)

### Monitoring
- Check logs in `logs/` directory
- Monitor bot performance and user activity
- Set up alerts for critical errors

### Scaling
- Use process managers (PM2, systemd)
- Consider load balancing for high traffic
- Monitor database performance

### Backup
- Regular MongoDB backups
- Environment configuration backups
- Log file rotation and archival

## File Structure
```
├── bot.py                 # Main bot application
├── config.py             # Configuration management
├── requirements.txt      # Python dependencies
├── .env.example         # Environment template
├── src/
│   ├── handlers/        # Command and callback handlers
│   ├── services/        # Business logic services
│   ├── models/          # Data models
│   ├── utils/           # Utility functions
│   └── database.py      # Database connection
└── logs/                # Application logs
```

## Support
For deployment issues, check:
1. Bot logs in `logs/bot.log`
2. Environment configuration
3. Database connectivity
4. Telegram API status

## Updates
To update the bot:
1. Pull latest changes
2. Update dependencies: `pip install -r requirements.txt`
3. Restart the bot service
