"""
Task Management Service for handling all task-related operations
"""

import uuid
import logging
from datetime import datetime, timezone, timedelta
from typing import List, Optional, Dict, Any, Tuple
from motor.motor_asyncio import AsyncIOMotorDatabase
from telegram import Bo<PERSON>
from telegram.error import TelegramError

from src.models.task import Task, UserTask, TaskSubmission, TaskType, UserTaskStatus, SubmissionStatus
from src.utils.logger import log_admin_action, log_user_action

logger = logging.getLogger(__name__)


class TaskManagementService:
    """Service for managing tasks, user task progress, and submissions"""

    def __init__(self, database: AsyncIOMotorDatabase, bot_token: str = None):
        self.db = database
        self.tasks_collection = database.tasks
        self.user_tasks_collection = database.user_tasks
        self.task_submissions_collection = database.task_submissions
        self.bot = Bot(token=bot_token) if bot_token else None
    
    # ==================== TASK MANAGEMENT ====================
    
    async def create_task(self, task_data: Dict[str, Any], created_by: int) -> Tuple[bool, str, Optional[Task]]:
        """Create a new task"""
        try:
            # Validate admin task creation
            is_valid, validation_message = await self.validate_admin_task_creation(created_by, task_data)
            if not is_valid:
                return False, validation_message, None

            # Sanitize input data
            if 'task_name' in task_data:
                task_data['task_name'] = await self.sanitize_task_input(task_data['task_name'])
            if 'caption' in task_data:
                task_data['caption'] = await self.sanitize_task_input(task_data['caption'])

            # Generate unique task ID
            task_id = str(uuid.uuid4())

            # Create task object
            task_data['task_id'] = task_id
            task_data['created_by'] = created_by
            task = Task.from_dict(task_data)

            # Validate task configuration
            is_valid, error_message = task.validate_configuration()
            if not is_valid:
                return False, error_message, None

            # For join channel tasks, verify bot admin access
            if task.is_join_channel_task():
                has_admin, admin_message = await self.verify_bot_admin_status(task.channel_id)
                if not has_admin:
                    return False, f"Bot admin verification failed: {admin_message}", None

            # Save to database
            await self.tasks_collection.insert_one(task.to_dict())

            # Log admin action
            await log_admin_action(
                created_by,
                'TASK_CREATED',
                f"Created {task.task_type.value} task: {task.task_name} (💎{task.reward_amount})"
            )

            logger.info(f"Task created: {task_id} by admin {created_by}")
            return True, "Task created successfully", task

        except Exception as e:
            logger.error(f"Error creating task: {e}")
            return False, f"Failed to create task: {str(e)}", None
    
    async def get_task(self, task_id: str) -> Optional[Task]:
        """Get a task by ID"""
        try:
            task_data = await self.tasks_collection.find_one({"task_id": task_id})
            if task_data:
                return Task.from_dict(task_data)
            return None
        except Exception as e:
            logger.error(f"Error getting task {task_id}: {e}")
            return None
    
    async def get_all_tasks(self, include_inactive: bool = False) -> List[Task]:
        """Get all tasks"""
        try:
            query = {} if include_inactive else {"is_active": True}
            tasks_data = await self.tasks_collection.find(query).to_list(None)
            return [Task.from_dict(task_data) for task_data in tasks_data]
        except Exception as e:
            logger.error(f"Error getting tasks: {e}")
            return []
    
    async def get_available_tasks_for_user(self, user_id: int) -> List[Dict[str, Any]]:
        """Get available tasks for a specific user"""
        try:
            # Get all active tasks
            active_tasks = await self.get_all_tasks(include_inactive=False)
            
            # Get user's task progress
            user_tasks = await self.get_user_tasks(user_id)
            user_task_map = {ut.task_id: ut for ut in user_tasks}
            
            available_tasks = []
            for task in active_tasks:
                user_task = user_task_map.get(task.task_id)
                
                # Determine if task is available
                if not user_task or user_task.can_retry():
                    status = "available"
                    completed = False
                elif user_task.is_completed():
                    status = "completed"
                    completed = True
                elif user_task.is_pending_review():
                    status = "pending_review"
                    completed = False
                else:
                    status = "in_progress"
                    completed = False
                
                available_tasks.append({
                    "task_id": task.task_id,
                    "title": task.task_name,
                    "description": task.get_task_description(),
                    "reward": task.reward_amount,
                    "task_type": task.task_type.value,
                    "status": status,
                    "completed": completed,
                    "channel_id": task.channel_id,
                    "join_link": task.join_link,
                    "reference_image": task.reference_image,
                    "caption": task.caption
                })
            
            return available_tasks
            
        except Exception as e:
            logger.error(f"Error getting available tasks for user {user_id}: {e}")
            return []
    
    async def update_task(self, task_id: str, updates: Dict[str, Any], updated_by: int) -> Tuple[bool, str]:
        """Update a task"""
        try:
            result = await self.tasks_collection.update_one(
                {"task_id": task_id},
                {"$set": updates}
            )
            
            if result.modified_count > 0:
                await log_admin_action(updated_by, 'TASK_UPDATED', f"Updated task: {task_id}")
                return True, "Task updated successfully"
            else:
                return False, "Task not found"
                
        except Exception as e:
            logger.error(f"Error updating task {task_id}: {e}")
            return False, f"Failed to update task: {str(e)}"
    
    async def delete_task(self, task_id: str, deleted_by: int) -> Tuple[bool, str]:
        """Soft delete a task"""
        try:
            result = await self.tasks_collection.update_one(
                {"task_id": task_id},
                {"$set": {"is_active": False}}
            )
            
            if result.modified_count > 0:
                await log_admin_action(deleted_by, 'TASK_DELETED', f"Deleted task: {task_id}")
                return True, "Task deleted successfully"
            else:
                return False, "Task not found"
                
        except Exception as e:
            logger.error(f"Error deleting task {task_id}: {e}")
            return False, f"Failed to delete task: {str(e)}"
    
    # ==================== USER TASK PROGRESS ====================
    
    async def get_user_task(self, user_id: int, task_id: str) -> Optional[UserTask]:
        """Get user's progress on a specific task"""
        try:
            user_task_data = await self.user_tasks_collection.find_one({
                "user_id": user_id,
                "task_id": task_id
            })
            if user_task_data:
                return UserTask.from_dict(user_task_data)
            return None
        except Exception as e:
            logger.error(f"Error getting user task {user_id}/{task_id}: {e}")
            return None
    
    async def get_user_tasks(self, user_id: int) -> List[UserTask]:
        """Get all user's task progress"""
        try:
            user_tasks_data = await self.user_tasks_collection.find({"user_id": user_id}).to_list(None)
            return [UserTask.from_dict(data) for data in user_tasks_data]
        except Exception as e:
            logger.error(f"Error getting user tasks for {user_id}: {e}")
            return []
    
    async def start_task(self, user_id: int, task_id: str) -> Tuple[bool, str]:
        """Start a task for a user"""
        try:
            # Check user ban status
            is_allowed, ban_message = await self.check_user_ban_status(user_id)
            if not is_allowed:
                return False, ban_message

            # Validate task eligibility
            is_eligible, eligibility_message = await self.validate_task_eligibility(user_id, task_id)
            if not is_eligible:
                return False, eligibility_message

            # Check if user already has progress on this task
            user_task = await self.get_user_task(user_id, task_id)

            if user_task and not user_task.can_retry():
                return False, "Task already started or completed"

            # Create or update user task
            if user_task:
                user_task.mark_started()
                await self.user_tasks_collection.replace_one(
                    {"user_id": user_id, "task_id": task_id},
                    user_task.to_dict()
                )
            else:
                user_task = UserTask(user_id=user_id, task_id=task_id)
                user_task.mark_started()
                await self.user_tasks_collection.insert_one(user_task.to_dict())

            # Log task action
            await self.log_task_action(user_id, 'TASK_STARTED', task_id, "User started task")

            return True, "Task started successfully"

        except Exception as e:
            logger.error(f"Error starting task {task_id} for user {user_id}: {e}")
            return False, f"Failed to start task: {str(e)}"
    
    async def complete_task(self, user_id: int, task_id: str, reviewed_by: int = None) -> Tuple[bool, str, float]:
        """Complete a task for a user and return reward amount"""
        try:
            # Get task and user task
            task = await self.get_task(task_id)
            if not task:
                return False, "Task not found", 0.0
            
            user_task = await self.get_user_task(user_id, task_id)
            if not user_task:
                return False, "User has not started this task", 0.0
            
            if user_task.is_completed():
                return False, "Task already completed", 0.0
            
            # Mark as completed
            user_task.mark_completed(reviewed_by)
            await self.user_tasks_collection.replace_one(
                {"user_id": user_id, "task_id": task_id},
                user_task.to_dict()
            )
            
            # Update task statistics
            await self.tasks_collection.update_one(
                {"task_id": task_id},
                {
                    "$inc": {
                        "total_completions": 1,
                        "total_rewards_distributed": task.reward_amount
                    }
                }
            )
            
            logger.info(f"Task {task_id} completed by user {user_id}")
            return True, "Task completed successfully", task.reward_amount
            
        except Exception as e:
            logger.error(f"Error completing task {task_id} for user {user_id}: {e}")
            return False, f"Failed to complete task: {str(e)}", 0.0

    # ==================== TASK SUBMISSIONS ====================

    async def submit_task(self, user_id: int, task_id: str, image_path: str = None, text: str = None) -> Tuple[bool, str]:
        """Submit a task for review"""
        try:
            # Check user ban status
            is_allowed, ban_message = await self.check_user_ban_status(user_id)
            if not is_allowed:
                return False, ban_message

            # Check submission rate limit
            rate_ok, rate_message = await self.check_submission_rate_limit(user_id)
            if not rate_ok:
                return False, rate_message

            # Check if task exists and is image submission type
            task = await self.get_task(task_id)
            if not task:
                return False, "Task not found"

            if not task.is_image_submission_task():
                return False, "This task does not require submission"

            # Check user task status
            user_task = await self.get_user_task(user_id, task_id)
            if not user_task:
                # Start the task first
                success, message = await self.start_task(user_id, task_id)
                if not success:
                    return False, message
                user_task = await self.get_user_task(user_id, task_id)

            if user_task.is_completed():
                return False, "Task already completed"

            if user_task.is_pending_review():
                return False, "Task already submitted for review"

            # Sanitize submission text
            sanitized_text = await self.sanitize_task_input(text) if text else None

            # Create submission
            submission_id = str(uuid.uuid4())
            submission = TaskSubmission(
                submission_id=submission_id,
                user_id=user_id,
                task_id=task_id,
                submitted_image=image_path,
                submission_text=sanitized_text
            )

            # Save submission
            await self.task_submissions_collection.insert_one(submission.to_dict())

            # Update user task status
            user_task.mark_submitted()
            await self.user_tasks_collection.replace_one(
                {"user_id": user_id, "task_id": task_id},
                user_task.to_dict()
            )

            # Log task action
            await self.log_task_action(user_id, 'TASK_SUBMITTED', task_id, "User submitted task for review")

            logger.info(f"Task submission created: {submission_id} for user {user_id}")
            return True, "Task submitted for review"

        except Exception as e:
            logger.error(f"Error submitting task {task_id} for user {user_id}: {e}")
            return False, f"Failed to submit task: {str(e)}"

    async def get_pending_submissions(self) -> List[Dict[str, Any]]:
        """Get all pending task submissions for admin review"""
        try:
            submissions_data = await self.task_submissions_collection.find({
                "review_status": SubmissionStatus.PENDING.value
            }).to_list(None)

            submissions = []
            for sub_data in submissions_data:
                submission = TaskSubmission.from_dict(sub_data)

                # Get task details
                task = await self.get_task(submission.task_id)

                submissions.append({
                    "submission_id": submission.submission_id,
                    "user_id": submission.user_id,
                    "task_id": submission.task_id,
                    "task_name": task.task_name if task else "Unknown Task",
                    "submitted_image": submission.submitted_image,
                    "submission_text": submission.submission_text,
                    "submission_date": submission.submission_date,
                    "reward_amount": task.reward_amount if task else 0
                })

            return submissions

        except Exception as e:
            logger.error(f"Error getting pending submissions: {e}")
            return []

    async def review_submission(self, submission_id: str, approved: bool, reviewed_by: int, notes: str = None) -> Tuple[bool, str, Dict[str, Any]]:
        """Review a task submission"""
        try:
            # Get submission
            submission_data = await self.task_submissions_collection.find_one({"submission_id": submission_id})
            if not submission_data:
                return False, "Submission not found", {}

            submission = TaskSubmission.from_dict(submission_data)

            if not submission.is_pending():
                return False, "Submission already reviewed", {}

            # Get task details
            task = await self.get_task(submission.task_id)
            if not task:
                return False, "Associated task not found", {}

            # Update submission status
            if approved:
                submission.approve(reviewed_by, notes)
                # Complete the user task
                success, message, reward = await self.complete_task(submission.user_id, submission.task_id, reviewed_by)
                if not success:
                    return False, f"Failed to complete task: {message}", {}
            else:
                submission.reject(reviewed_by, notes or "Submission does not meet requirements")
                # Reset user task to allow retry
                user_task = await self.get_user_task(submission.user_id, submission.task_id)
                if user_task:
                    user_task.mark_rejected(notes or "Submission rejected", reviewed_by)
                    await self.user_tasks_collection.replace_one(
                        {"user_id": submission.user_id, "task_id": submission.task_id},
                        user_task.to_dict()
                    )
                reward = 0.0

            # Save updated submission
            await self.task_submissions_collection.replace_one(
                {"submission_id": submission_id},
                submission.to_dict()
            )

            # Log admin action
            action = "TASK_SUBMISSION_APPROVED" if approved else "TASK_SUBMISSION_REJECTED"
            await log_admin_action(
                reviewed_by,
                action,
                f"Reviewed submission {submission_id} for user {submission.user_id}"
            )

            result_data = {
                "user_id": submission.user_id,
                "task_name": task.task_name,
                "reward_amount": reward,
                "approved": approved
            }

            return True, "Submission reviewed successfully", result_data

        except Exception as e:
            logger.error(f"Error reviewing submission {submission_id}: {e}")
            return False, f"Failed to review submission: {str(e)}", {}

    # ==================== STATISTICS ====================

    async def get_task_statistics(self) -> Dict[str, Any]:
        """Get task system statistics"""
        try:
            # Get total tasks
            total_tasks = await self.tasks_collection.count_documents({"is_active": True})

            # Get total completions
            pipeline = [
                {"$match": {"is_active": True}},
                {"$group": {
                    "_id": None,
                    "total_completions": {"$sum": "$total_completions"},
                    "total_rewards": {"$sum": "$total_rewards_distributed"}
                }}
            ]

            result = await self.tasks_collection.aggregate(pipeline).to_list(1)
            total_completions = result[0]["total_completions"] if result else 0
            total_rewards = result[0]["total_rewards"] if result else 0.0

            # Get pending submissions
            pending_submissions = await self.task_submissions_collection.count_documents({
                "review_status": SubmissionStatus.PENDING.value
            })

            return {
                "total_active_tasks": total_tasks,
                "total_completions": total_completions,
                "total_rewards_distributed": total_rewards,
                "pending_submissions": pending_submissions
            }

        except Exception as e:
            logger.error(f"Error getting task statistics: {e}")
            return {
                "total_active_tasks": 0,
                "total_completions": 0,
                "total_rewards_distributed": 0.0,
                "pending_submissions": 0
            }

    # ==================== CHANNEL VERIFICATION ====================

    async def verify_channel_membership(self, user_id: int, channel_id: str) -> Tuple[bool, str]:
        """Verify if user is a member of the specified channel"""
        try:
            if not self.bot:
                return False, "Bot instance not available for verification"

            # Check if user is a member of the channel
            try:
                member = await self.bot.get_chat_member(chat_id=channel_id, user_id=user_id)

                # Check membership status
                if member.status in ['member', 'administrator', 'creator']:
                    return True, "User is a member of the channel"
                elif member.status == 'left':
                    return False, "User has left the channel"
                elif member.status == 'kicked':
                    return False, "User is banned from the channel"
                else:
                    return False, f"User status: {member.status}"

            except TelegramError as e:
                if "user not found" in str(e).lower():
                    return False, "User not found in channel"
                elif "chat not found" in str(e).lower():
                    return False, "Channel not found"
                elif "bot is not a member" in str(e).lower():
                    return False, "Bot is not a member of the channel"
                else:
                    logger.error(f"Telegram error verifying membership: {e}")
                    return False, f"Verification failed: {str(e)}"

        except Exception as e:
            logger.error(f"Error verifying channel membership for user {user_id} in {channel_id}: {e}")
            return False, f"Verification error: {str(e)}"

    async def verify_bot_admin_status(self, channel_id: str) -> Tuple[bool, str]:
        """Verify if bot has admin privileges in the channel"""
        try:
            if not self.bot:
                return False, "Bot instance not available"

            try:
                # Get bot's member status in the channel
                bot_info = await self.bot.get_me()
                member = await self.bot.get_chat_member(chat_id=channel_id, user_id=bot_info.id)

                if member.status in ['administrator', 'creator']:
                    return True, "Bot has admin privileges"
                else:
                    return False, f"Bot status: {member.status}"

            except TelegramError as e:
                if "chat not found" in str(e).lower():
                    return False, "Channel not found"
                elif "bot is not a member" in str(e).lower():
                    return False, "Bot is not a member of the channel"
                else:
                    logger.error(f"Telegram error checking bot admin status: {e}")
                    return False, f"Check failed: {str(e)}"

        except Exception as e:
            logger.error(f"Error checking bot admin status in {channel_id}: {e}")
            return False, f"Check error: {str(e)}"

    async def process_join_channel_task(self, user_id: int, task_id: str) -> Tuple[bool, str, float]:
        """Process join channel task completion"""
        try:
            # Get task details
            task = await self.get_task(task_id)
            if not task:
                return False, "Task not found", 0.0

            if not task.is_join_channel_task():
                return False, "This is not a join channel task", 0.0

            if not task.channel_id:
                return False, "Channel ID not configured for this task", 0.0

            # Verify channel membership
            is_member, message = await self.verify_channel_membership(user_id, task.channel_id)

            if not is_member:
                return False, f"Channel verification failed: {message}", 0.0

            # Complete the task
            success, completion_message, reward = await self.complete_task(user_id, task_id)

            if success:
                return True, "Channel membership verified and task completed!", reward
            else:
                return False, f"Failed to complete task: {completion_message}", 0.0

        except Exception as e:
            logger.error(f"Error processing join channel task {task_id} for user {user_id}: {e}")
            return False, f"Processing error: {str(e)}", 0.0

    # ==================== SECURITY & VALIDATION ====================

    async def check_submission_rate_limit(self, user_id: int) -> Tuple[bool, str]:
        """Check if user is within submission rate limits"""
        try:
            # Allow max 5 submissions per hour
            one_hour_ago = datetime.now(timezone.utc) - timedelta(hours=1)

            recent_submissions = await self.task_submissions_collection.count_documents({
                "user_id": user_id,
                "submission_date": {"$gte": one_hour_ago.isoformat()}
            })

            if recent_submissions >= 5:
                return False, "Rate limit exceeded. Please wait before submitting more tasks."

            return True, "Rate limit check passed"

        except Exception as e:
            logger.error(f"Error checking submission rate limit: {e}")
            return True, "Rate limit check failed, allowing submission"

    async def validate_task_eligibility(self, user_id: int, task_id: str) -> Tuple[bool, str]:
        """Validate if user is eligible to start/complete a task"""
        try:
            # Get task
            task = await self.get_task(task_id)
            if not task:
                return False, "Task not found"

            if not task.is_active:
                return False, "Task is no longer active"

            # Check if user has already completed this task
            user_task = await self.get_user_task(user_id, task_id)
            if user_task and user_task.is_completed():
                return False, "Task already completed"

            # For join channel tasks, verify bot has admin access
            if task.is_join_channel_task():
                has_admin, admin_message = await self.verify_bot_admin_status(task.channel_id)
                if not has_admin:
                    await log_admin_action(
                        0,  # System action
                        'TASK_VALIDATION_FAILED',
                        f"Bot lacks admin access to channel {task.channel_id} for task {task_id}"
                    )
                    return False, f"Task configuration error: {admin_message}"

            return True, "User is eligible for this task"

        except Exception as e:
            logger.error(f"Error validating task eligibility: {e}")
            return False, f"Validation error: {str(e)}"

    async def log_task_action(self, user_id: int, action: str, task_id: str, details: str = ""):
        """Log task-related user actions"""
        try:
            await log_user_action(
                user_id,
                action,
                f"Task {task_id}: {details}"
            )
        except Exception as e:
            logger.error(f"Error logging task action: {e}")

    async def validate_admin_task_creation(self, admin_id: int, task_data: Dict[str, Any]) -> Tuple[bool, str]:
        """Validate admin task creation request"""
        try:
            # Check if admin has created too many tasks recently
            one_day_ago = datetime.now(timezone.utc) - timedelta(days=1)

            recent_tasks = await self.tasks_collection.count_documents({
                "created_by": admin_id,
                "created_date": {"$gte": one_day_ago.isoformat()}
            })

            if recent_tasks >= 10:  # Max 10 tasks per day per admin
                return False, "Daily task creation limit exceeded (10 tasks per day)"

            # Validate reward amount
            reward = task_data.get('reward_amount', 0)
            if reward < 1 or reward > 500:
                return False, "Reward amount must be between 💎1 and 💎500"

            # Validate task name
            task_name = task_data.get('task_name', '').strip()
            if not task_name or len(task_name) < 3:
                return False, "Task name must be at least 3 characters long"

            if len(task_name) > 100:
                return False, "Task name must be less than 100 characters"

            # Check for duplicate task names
            existing_task = await self.tasks_collection.find_one({
                "task_name": task_name,
                "is_active": True
            })

            if existing_task:
                return False, "A task with this name already exists"

            return True, "Task creation validation passed"

        except Exception as e:
            logger.error(f"Error validating admin task creation: {e}")
            return False, f"Validation error: {str(e)}"

    async def sanitize_task_input(self, input_text: str) -> str:
        """Sanitize user input for task-related text"""
        try:
            if not input_text:
                return ""

            # Remove potentially harmful characters
            sanitized = input_text.strip()

            # Remove excessive whitespace
            sanitized = ' '.join(sanitized.split())

            # Limit length
            if len(sanitized) > 1000:
                sanitized = sanitized[:1000] + "..."

            return sanitized

        except Exception as e:
            logger.error(f"Error sanitizing input: {e}")
            return str(input_text)[:100]  # Fallback to truncated original

    async def check_user_ban_status(self, user_id: int) -> Tuple[bool, str]:
        """Check if user is banned from task system"""
        try:
            # Check for recent rejections (3 rejections in 24 hours = temporary ban)
            one_day_ago = datetime.now(timezone.utc) - timedelta(days=1)

            recent_rejections = await self.user_tasks_collection.count_documents({
                "user_id": user_id,
                "status": UserTaskStatus.REJECTED.value,
                "review_date": {"$gte": one_day_ago.isoformat()}
            })

            if recent_rejections >= 3:
                return False, "Temporarily banned from task system due to multiple rejected submissions"

            return True, "User is not banned"

        except Exception as e:
            logger.error(f"Error checking user ban status: {e}")
            return True, "Ban check failed, allowing access"
