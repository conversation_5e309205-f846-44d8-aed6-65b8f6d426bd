"""
Keyboard utilities for the Telegram Referral Bot
"""

from telegram import InlineKeyboardButton, InlineKeyboardMarkup, ReplyKeyboardMarkup, KeyboardButton
from typing import List, Optional
from config import Config

class Keyboards:
    """Keyboard generator for the bot"""
    
    @staticmethod
    def main_menu() -> ReplyKeyboardMarkup:
        """Main menu keyboard"""
        keyboard = [
            [Keyboard<PERSON>utton("💰 Balance"), KeyboardButton("🎁 Daily Bonus")],
            [KeyboardButton("💸 Withdraw"), KeyboardButton("👥 Referrals")],
            [KeyboardButton("⚙️ Settings"), KeyboardButton("📊 Statistics")]
        ]
        
        return ReplyKeyboardMarkup(
            keyboard,
            resize_keyboard=True,
            one_time_keyboard=False
        )
    
    @staticmethod
    def admin_menu() -> ReplyKeyboardMarkup:
        """Admin menu keyboard"""
        keyboard = [
            [KeyboardButton("👥 Users"), KeyboardButton("💸 Withdrawals")],
            [KeyboardButton("📢 Channels"), KeyboardButton("🛍️ Products")],
            [KeyboardButton("📊 Analytics"), KeyboardButton("⚙️ Settings")],
            [KeyboardButton("📨 Broadcast"), KeyboardButton("🔙 Back to User")]
        ]
        
        return ReplyKeyboardMarkup(
            keyboard,
            resize_keyboard=True,
            one_time_keyboard=False
        )
    
    @staticmethod
    def balance_menu() -> InlineKeyboardMarkup:
        """Balance menu inline keyboard"""
        keyboard = [
            [InlineKeyboardButton("📈 Transaction History", callback_data="balance_history")],
            [InlineKeyboardButton("💸 Withdraw", callback_data="withdraw_menu")],
            [InlineKeyboardButton("🔄 Refresh", callback_data="balance_refresh")]
        ]
        
        return InlineKeyboardMarkup(keyboard)
    
    @staticmethod
    def referral_menu() -> InlineKeyboardMarkup:
        """Referral menu inline keyboard"""
        keyboard = [
            [InlineKeyboardButton("📋 Copy Link", callback_data="copy_referral_link")],
            [InlineKeyboardButton("📊 My Referrals", callback_data="my_referrals")],
            [InlineKeyboardButton("🏆 Leaderboard", callback_data="referral_leaderboard")]
        ]
        
        return InlineKeyboardMarkup(keyboard)
    
    @staticmethod
    def withdrawal_menu(user_balance: float) -> InlineKeyboardMarkup:
        """Withdrawal menu inline keyboard"""
        keyboard = []
        
        if user_balance >= Config.MINIMUM_WITHDRAWAL:
            keyboard.append([InlineKeyboardButton("🛍️ Digital Products", callback_data="withdraw_products")])
            keyboard.append([InlineKeyboardButton("💰 Cash Withdrawal", callback_data="withdraw_cash")])
        else:
            needed = Config.MINIMUM_WITHDRAWAL - user_balance
            keyboard.append([InlineKeyboardButton(f"❌ Need 💎{needed:.2f} more", callback_data="insufficient_balance")])
        
        keyboard.append([InlineKeyboardButton("📋 Withdrawal History", callback_data="withdrawal_history")])
        
        return InlineKeyboardMarkup(keyboard)
    
    @staticmethod
    def products_menu(products: List[dict], page: int = 0, per_page: int = 5) -> InlineKeyboardMarkup:
        """Products menu inline keyboard with pagination"""
        keyboard = []
        
        # Calculate pagination
        start_idx = page * per_page
        end_idx = start_idx + per_page
        page_products = products[start_idx:end_idx]
        
        # Add product buttons
        for product in page_products:
            button_text = f"🛍️ {product['name']} - 💎{product['price']}"
            callback_data = f"product_{product['product_id']}"
            keyboard.append([InlineKeyboardButton(button_text, callback_data=callback_data)])
        
        # Add pagination buttons
        nav_buttons = []
        if page > 0:
            nav_buttons.append(InlineKeyboardButton("⬅️ Previous", callback_data=f"products_page_{page-1}"))
        
        if end_idx < len(products):
            nav_buttons.append(InlineKeyboardButton("➡️ Next", callback_data=f"products_page_{page+1}"))
        
        if nav_buttons:
            keyboard.append(nav_buttons)
        
        # Add back button
        keyboard.append([InlineKeyboardButton("🔙 Back", callback_data="withdraw_menu")])
        
        return InlineKeyboardMarkup(keyboard)
    
    @staticmethod
    def product_details(product_id: str) -> InlineKeyboardMarkup:
        """Product details inline keyboard"""
        keyboard = [
            [InlineKeyboardButton("✅ Purchase", callback_data=f"purchase_{product_id}")],
            [InlineKeyboardButton("🔙 Back to Products", callback_data="withdraw_products")]
        ]
        
        return InlineKeyboardMarkup(keyboard)
    
    @staticmethod
    def confirm_purchase(product_id: str) -> InlineKeyboardMarkup:
        """Confirm purchase inline keyboard"""
        keyboard = [
            [InlineKeyboardButton("✅ Confirm Purchase", callback_data=f"confirm_purchase_{product_id}")],
            [InlineKeyboardButton("❌ Cancel", callback_data=f"product_{product_id}")]
        ]
        
        return InlineKeyboardMarkup(keyboard)
    
    @staticmethod
    def admin_user_actions(user_id: int) -> InlineKeyboardMarkup:
        """Admin user actions inline keyboard"""
        keyboard = [
            [InlineKeyboardButton("💰 Add Balance", callback_data=f"admin_add_balance_{user_id}")],
            [InlineKeyboardButton("💸 Deduct Balance", callback_data=f"admin_deduct_balance_{user_id}")],
            [InlineKeyboardButton("🚫 Ban User", callback_data=f"admin_ban_user_{user_id}")],
            [InlineKeyboardButton("✅ Unban User", callback_data=f"admin_unban_user_{user_id}")],
            [InlineKeyboardButton("📊 User Stats", callback_data=f"admin_user_stats_{user_id}")],
            [InlineKeyboardButton("🔙 Back", callback_data="admin_users")]
        ]
        
        return InlineKeyboardMarkup(keyboard)
    
    @staticmethod
    def admin_withdrawal_actions(withdrawal_id: str) -> InlineKeyboardMarkup:
        """Admin withdrawal actions inline keyboard"""
        keyboard = [
            [InlineKeyboardButton("✅ Approve", callback_data=f"admin_approve_withdrawal_{withdrawal_id}")],
            [InlineKeyboardButton("❌ Reject", callback_data=f"admin_reject_withdrawal_{withdrawal_id}")],
            [InlineKeyboardButton("✅ Mark Completed", callback_data=f"admin_complete_withdrawal_{withdrawal_id}")],
            [InlineKeyboardButton("🔙 Back", callback_data="admin_withdrawals")]
        ]
        
        return InlineKeyboardMarkup(keyboard)
    
    @staticmethod
    def admin_channel_actions(channel_id: str) -> InlineKeyboardMarkup:
        """Admin channel actions inline keyboard"""
        keyboard = [
            [InlineKeyboardButton("✅ Activate", callback_data=f"admin_activate_channel_{channel_id}")],
            [InlineKeyboardButton("❌ Deactivate", callback_data=f"admin_deactivate_channel_{channel_id}")],
            [InlineKeyboardButton("🗑️ Remove", callback_data=f"admin_remove_channel_{channel_id}")],
            [InlineKeyboardButton("🔙 Back", callback_data="admin_channels")]
        ]
        
        return InlineKeyboardMarkup(keyboard)
    
    @staticmethod
    def admin_product_actions(product_id: str) -> InlineKeyboardMarkup:
        """Admin product actions inline keyboard"""
        keyboard = [
            [InlineKeyboardButton("✏️ Edit", callback_data=f"admin_edit_product_{product_id}")],
            [InlineKeyboardButton("✅ Activate", callback_data=f"admin_activate_product_{product_id}")],
            [InlineKeyboardButton("❌ Deactivate", callback_data=f"admin_deactivate_product_{product_id}")],
            [InlineKeyboardButton("🗑️ Delete", callback_data=f"admin_delete_product_{product_id}")],
            [InlineKeyboardButton("🔙 Back", callback_data="admin_products")]
        ]
        
        return InlineKeyboardMarkup(keyboard)
    
    @staticmethod
    def pagination_keyboard(current_page: int, total_pages: int, callback_prefix: str) -> InlineKeyboardMarkup:
        """Generic pagination keyboard"""
        keyboard = []
        
        nav_buttons = []
        if current_page > 0:
            nav_buttons.append(InlineKeyboardButton("⬅️", callback_data=f"{callback_prefix}_{current_page-1}"))
        
        nav_buttons.append(InlineKeyboardButton(f"{current_page + 1}/{total_pages}", callback_data="noop"))
        
        if current_page < total_pages - 1:
            nav_buttons.append(InlineKeyboardButton("➡️", callback_data=f"{callback_prefix}_{current_page+1}"))
        
        keyboard.append(nav_buttons)
        
        return InlineKeyboardMarkup(keyboard)
    
    @staticmethod
    def confirmation_keyboard(confirm_callback: str, cancel_callback: str = "cancel") -> InlineKeyboardMarkup:
        """Generic confirmation keyboard"""
        keyboard = [
            [InlineKeyboardButton("✅ Confirm", callback_data=confirm_callback)],
            [InlineKeyboardButton("❌ Cancel", callback_data=cancel_callback)]
        ]
        
        return InlineKeyboardMarkup(keyboard)
    
    @staticmethod
    def channel_join_keyboard(channels: List[dict]) -> InlineKeyboardMarkup:
        """Channel join keyboard for forced subscription"""
        keyboard = []
        
        for channel in channels:
            button_text = f"📢 Join {channel['name']}"
            keyboard.append([InlineKeyboardButton(button_text, url=channel['join_link'])])
        
        keyboard.append([InlineKeyboardButton("✅ I've Joined All Channels", callback_data="verify_channels")])
        
        return InlineKeyboardMarkup(keyboard)
    
    @staticmethod
    def settings_menu() -> InlineKeyboardMarkup:
        """Settings menu inline keyboard"""
        keyboard = [
            [InlineKeyboardButton("🔔 Notifications", callback_data="settings_notifications")],
            [InlineKeyboardButton("🌐 Language", callback_data="settings_language")],
            [InlineKeyboardButton("📱 Account Info", callback_data="settings_account")],
            [InlineKeyboardButton("❓ Help", callback_data="settings_help")]
        ]
        
        return InlineKeyboardMarkup(keyboard)
