@echo off
echo 🤖 Starting Telegram Referral Bot...

REM Check if virtual environment exists
if not exist "venv" (
    echo Creating virtual environment...
    python -m venv venv
)

REM Activate virtual environment
call venv\Scripts\activate

REM Install/update dependencies
echo Installing dependencies...
pip install -r requirements.txt

REM Check if .env file exists
if not exist ".env" (
    echo ❌ Error: .env file not found!
    echo Please copy .env.example to .env and configure it.
    pause
    exit /b 1
)

REM Create logs directory
if not exist "logs" mkdir logs

REM Start the bot
echo 🚀 Starting bot...
python bot.py

pause
